import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/bill.dart';
import '../utils/bill_fetcher.dart';
import '../utils/currency_formatter.dart';

class UtilityBillsScreen extends StatefulWidget {
  const UtilityBillsScreen({super.key});

  @override
  State<UtilityBillsScreen> createState() => _UtilityBillsScreenState();
}

class _UtilityBillsScreenState extends State<UtilityBillsScreen> {
  List<Bill> _utilityBills = [];
  UtilityType _selectedUtilityType = UtilityType.water;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadUtilityBills();
  }

  Future<void> _loadUtilityBills() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final bills = await BillFetcher.fetchBillsByType(BillType.utility);
      setState(() {
        _utilityBills = bills;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<Bill> get _filteredBills {
    return _utilityBills
        .where((bill) => bill.utilityType == _selectedUtilityType)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Utility Bills'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUtilityBills,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _utilityBills.isEmpty
                  ? _buildEmptyState()
                  : Column(
                      children: [
                        _buildUtilityTypeSelector(),
                        const SizedBox(height: 16),
                        _buildUtilitySummary(),
                        const SizedBox(height: 16),
                        Expanded(
                          child: _filteredBills.isEmpty
                              ? _buildEmptyUtilityTypeState()
                              : _buildUtilityBillsList(),
                        ),
                      ],
                    ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading utility bills',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadUtilityBills,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.power,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No utility bills found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your utility bills will appear here when they become available.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyUtilityTypeState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getUtilityIcon(_selectedUtilityType),
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No ${_selectedUtilityType.name} bills found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your ${_selectedUtilityType.name} bills will appear here when they become available.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUtilityTypeSelector() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: UtilityType.values.map((type) {
          final isSelected = type == _selectedUtilityType;
          
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: ChoiceChip(
              label: Row(
                children: [
                  Icon(
                    _getUtilityIcon(type),
                    size: 18,
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _capitalizeFirst(type.name),
                    style: TextStyle(
                      color: isSelected 
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedUtilityType = type;
                  });
                }
              },
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHigh,
              selectedColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildUtilitySummary() {
    // Get all bills of the selected utility type
    final bills = _filteredBills;
    
    if (bills.isEmpty) {
      return const SizedBox.shrink();
    }

    // Calculate total amount
    final totalAmount = bills.fold<double>(
        0, (total, bill) => total + bill.amount);
    
    // Get latest bill for current readings
    final latestBill = bills.reduce((a, b) => 
        a.createdAt.isAfter(b.createdAt) ? a : b);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getUtilityIcon(_selectedUtilityType),
                  color: _getUtilityColor(_selectedUtilityType),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  '${_capitalizeFirst(_selectedUtilityType.name)} Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const Divider(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildSummaryItem(
                  'Total Bills',
                  '${bills.length}',
                  Icons.receipt_long,
                ),
                _buildSummaryItem(
                  'Current Period',
                  latestBill.unitConsumed != null
                      ? '${latestBill.unitConsumed!.toStringAsFixed(1)} units'
                      : 'N/A',
                  Icons.speed,
                ),
                _buildSummaryItem(
                  'Total Amount',
                  CurrencyFormatter.format(context, totalAmount),
                  Icons.payments,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildUtilityBillsList() {
    final bills = _filteredBills;
    
    // Sort by date, newest first
    bills.sort((a, b) => b.dueDate.compareTo(a.dueDate));
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bills.length,
      itemBuilder: (context, index) {
        final bill = bills[index];
        return _buildUtilityBillCard(bill);
      },
    );
  }

  Widget _buildUtilityBillCard(Bill bill) {
    final isPaid = bill.status == BillStatus.paid;
    final isOverdue = bill.isOverdue;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isOverdue && !isPaid
            ? BorderSide(color: Colors.red.shade400, width: 1.5)
            : isPaid 
                ? BorderSide(color: Colors.green.shade300, width: 1)
                : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getUtilityColor(_selectedUtilityType).withAlpha(40),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getUtilityIcon(_selectedUtilityType),
                    color: _getUtilityColor(_selectedUtilityType),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bill.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        'Bill Period: ${DateFormat('MMM d').format(bill.createdAt)} - ${DateFormat('MMM d, y').format(bill.dueDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      CurrencyFormatter.format(context, bill.amount),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isOverdue && !isPaid 
                            ? Colors.red.shade700
                            : isPaid
                                ? Colors.green.shade700
                                : Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: isPaid
                            ? Colors.green.shade100
                            : isOverdue
                                ? Colors.red.shade100
                                : Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isPaid
                            ? 'Paid'
                            : isOverdue
                                ? 'Overdue'
                                : 'Pending',
                        style: TextStyle(
                          color: isPaid
                              ? Colors.green.shade800
                              : isOverdue
                                  ? Colors.red.shade800
                                  : Colors.orange.shade800,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),
            
            // Utility details
            if (bill.description.isNotEmpty) ...[
              Text(
                bill.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
            ],
            
            // Meter readings
            if (bill.previousMeterReading != null || bill.currentMeterReading != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerLow,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildMeterReadingItem(
                          'Previous Reading',
                          bill.previousMeterReading != null
                              ? bill.previousMeterReading!.toStringAsFixed(1)
                              : 'N/A',
                        ),
                        _buildMeterReadingItem(
                          'Current Reading',
                          bill.currentMeterReading != null
                              ? bill.currentMeterReading!.toStringAsFixed(1)
                              : 'N/A',
                        ),
                        _buildMeterReadingItem(
                          'Units Consumed',
                          bill.unitConsumed != null
                              ? bill.unitConsumed!.toStringAsFixed(1)
                              : 'N/A',
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildMeterReadingItem(
                          'Rate per Unit',
                          bill.ratePerUnit != null
                              ? CurrencyFormatter.format(context, bill.ratePerUnit!)
                              : 'N/A',
                        ),
                        _buildMeterReadingItem(
                          'Base Amount',
                          bill.unitConsumed != null && bill.ratePerUnit != null
                              ? CurrencyFormatter.format(
                                  context, bill.unitConsumed! * bill.ratePerUnit!)
                              : 'N/A',
                        ),
                        _buildMeterReadingItem(
                          'Additional Charges',
                          bill.billComponents != null && bill.billComponents!.isNotEmpty
                              ? CurrencyFormatter.format(
                                  context,
                                  bill.amount -
                                      ((bill.unitConsumed ?? 0) *
                                          (bill.ratePerUnit ?? 0)))
                              : 'N/A',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Due date info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: isOverdue && !isPaid 
                          ? Colors.red.shade700
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Due ${DateFormat('MMM d, y').format(bill.dueDate)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isOverdue && !isPaid 
                            ? Colors.red.shade700
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: isOverdue && !isPaid 
                            ? FontWeight.bold 
                            : FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                if (isPaid && bill.paidAt != null)
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.green.shade500),
                        ),
                        child: Icon(
                          Icons.verified,
                          size: 12,
                          color: Colors.green.shade700,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Paid ${DateFormat('MMM d').format(bill.paidAt!)}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeterReadingItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  IconData _getUtilityIcon(UtilityType type) {
    switch (type) {
      case UtilityType.water:
        return Icons.water_drop;
      case UtilityType.electricity:
        return Icons.electric_bolt;
      case UtilityType.gas:
        return Icons.local_fire_department;
      case UtilityType.internet:
        return Icons.wifi;
      case UtilityType.other:
        return Icons.miscellaneous_services;
    }
  }

  Color _getUtilityColor(UtilityType type) {
    switch (type) {
      case UtilityType.water:
        return Colors.blue;
      case UtilityType.electricity:
        return Colors.amber;
      case UtilityType.gas:
        return Colors.deepOrange;
      case UtilityType.internet:
        return Colors.purple;
      case UtilityType.other:
        return Colors.teal;
    }
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
} 