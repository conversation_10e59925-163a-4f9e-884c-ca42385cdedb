import 'package:flutter/material.dart';

/// Theme constants for the Residence Hub app
class AppThemeConstants {
  /// Spacing constants
  static const double spacingXxs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;
  static const double spacingXxxl = 64.0;

  /// Border radius constants
  static const double radiusXs = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXl = 24.0;
  static const double radiusCircular = 999.0;

  /// Elevation constants
  static const double elevationNone = 0.0;
  static const double elevationXs = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXl = 16.0;

  /// Animation duration constants
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);

  /// Icon sizes
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXl = 48.0;

  /// Button sizes
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXl = 56.0;

  /// Input field sizes
  static const double inputHeightS = 40.0;
  static const double inputHeightM = 48.0;
  static const double inputHeightL = 56.0;

  /// Card padding
  static const EdgeInsets cardPaddingS = EdgeInsets.all(spacingS);
  static const EdgeInsets cardPaddingM = EdgeInsets.all(spacingM);
  static const EdgeInsets cardPaddingL = EdgeInsets.all(spacingL);

  /// Screen padding
  static const EdgeInsets screenPadding = EdgeInsets.all(spacingM);
  static const EdgeInsets screenPaddingHorizontal = EdgeInsets.symmetric(horizontal: spacingM);
  static const EdgeInsets screenPaddingVertical = EdgeInsets.symmetric(vertical: spacingM);

  /// Form field padding
  static const EdgeInsets formFieldPadding = EdgeInsets.symmetric(vertical: spacingS);

  /// Button padding
  static const EdgeInsets buttonPaddingS = EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingXs);
  static const EdgeInsets buttonPaddingM = EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingS);
  static const EdgeInsets buttonPaddingL = EdgeInsets.symmetric(horizontal: spacingXl, vertical: spacingM);

  /// Shadow constants
  static List<BoxShadow> get shadowS => [
        BoxShadow(
          color: Colors.black.withAlpha(26), // 0.1 opacity
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ];

  static List<BoxShadow> get shadowM => [
        BoxShadow(
          color: Colors.black.withAlpha(26), // 0.1 opacity
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ];

  static List<BoxShadow> get shadowL => [
        BoxShadow(
          color: Colors.black.withAlpha(26), // 0.1 opacity
          blurRadius: 16,
          offset: const Offset(0, 8),
        ),
      ];
} 