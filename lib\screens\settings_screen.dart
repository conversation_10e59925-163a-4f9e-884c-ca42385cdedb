import 'package:flutter/material.dart';
import 'package:currency_picker/currency_picker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/user_settings.dart';
import '../services/auth_service.dart';
import '../services/settings_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AuthService _authService = AuthService();
  final SettingsService _settingsService = SettingsService();

  bool _isLoading = true;
  UserSettings? _userSettings;
  String? _selectedCurrency;
  PackageInfo? _packageInfo;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final info = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _packageInfo = info;
        });
      }
    } catch (e) {
      // Handle the case where package_info_plus is not available
      // This can happen on some platforms or during development
      debugPrint('Error loading package info: $e');
      if (mounted) {
        setState(() {
          // Create a fallback PackageInfo with default values
          _packageInfo = PackageInfo(
            appName: 'Residence Hub',
            packageName: 'com.residencehub.app',
            version: '1.0.0',
            buildNumber: '1',
            buildSignature: '',
            installerStore: null,
          );
        });
      }
    }
  }

  Future<void> _loadSettings() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        final settings = await _settingsService.getUserSettings(userId);

        if (!mounted) return;

        setState(() {
          _userSettings = settings;
          _selectedCurrency = settings.currencyCode;
        });
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading settings: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateCurrency(String currencyCode) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        await _settingsService.updateCurrencyCode(userId, currencyCode);

        if (!mounted) return;

        setState(() {
          _selectedCurrency = currencyCode;
          if (_userSettings != null) {
            _userSettings = _userSettings!.copyWith(currencyCode: currencyCode);
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Currency updated successfully')),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error updating currency: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showCurrencyPicker() {
    showCurrencyPicker(
      context: context,
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
      favorite: ['KES', 'USD', 'EUR', 'GBP'],
      onSelect: (Currency currency) {
        _updateCurrency(currency.code);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Header Section
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Theme.of(context).primaryColor,
                          Theme.of(context).primaryColor.withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(24, 0, 24, 32),
                      child: Column(
                        children: [
                          Icon(
                            Icons.settings_rounded,
                            size: 64,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'App Settings',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Customize your experience',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Settings Content
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // App Settings Section
                        _buildSettingsSection(
                          context,
                          'App Settings',
                          Icons.tune,
                          [
                            _buildCurrencyTile(),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Support Section
                        _buildSettingsSection(
                          context,
                          'Support & Help',
                          Icons.support_agent,
                          [
                            _buildSettingsTile(
                              context,
                              'Contact Support',
                              'Get help from our support team',
                              Icons.headset_mic,
                              () => _showContactSupport(context),
                            ),
                            _buildSettingsTile(
                              context,
                              'FAQ & Help',
                              'Frequently asked questions',
                              Icons.help_outline,
                              () => _showFAQ(context),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Legal Section
                        _buildSettingsSection(
                          context,
                          'Legal & Privacy',
                          Icons.gavel,
                          [
                            _buildSettingsTile(
                              context,
                              'Privacy Policy',
                              'How we handle your data',
                              Icons.privacy_tip_outlined,
                              () => _showPrivacyPolicy(context),
                            ),
                            _buildSettingsTile(
                              context,
                              'Terms & Conditions',
                              'Terms of service and usage',
                              Icons.description_outlined,
                              () => _showTermsConditions(context),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // About Section
                        _buildSettingsSection(
                          context,
                          'About',
                          Icons.info_outline,
                          [
                            _buildAboutTile(),
                            _buildSettingsTile(
                              context,
                              'Changelog',
                              'What\'s new in this version',
                              Icons.update,
                              () => _showChangelog(context),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // App Version Footer
                        if (_packageInfo != null)
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.home_rounded,
                                  color: Theme.of(context).primaryColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Residence Hub v${_packageInfo!.version}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  String _getCurrencySymbol(String currencyCode) {
    try {
      final currency = CurrencyService().findByCode(currencyCode);
      return currency?.symbol ?? currencyCode;
    } catch (e) {
      return currencyCode;
    }
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyTile() {
    return InkWell(
      onTap: _showCurrencyPicker,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.attach_money,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Currency',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _selectedCurrency != null
                        ? '$_selectedCurrency (${_getCurrencySymbol(_selectedCurrency!)})'
                        : 'Select your preferred currency',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (_selectedCurrency != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _selectedCurrency!,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutTile() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.home_rounded,
              color: Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Residence Hub',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _packageInfo != null
                      ? 'Version ${_packageInfo!.version} (${_packageInfo!.buildNumber})'
                      : 'Loading version info...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Contact Support Methods
  void _showContactSupport(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.support_agent,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Contact Support',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      tooltip: 'Close',
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    _buildContactCard(
                      context,
                      icon: Icons.email,
                      title: 'Email Support',
                      subtitle: '<EMAIL>',
                      description: 'Send us an email for detailed inquiries',
                      onTap: () => _launchEmail(),
                    ),
                    const SizedBox(height: 12),
                    _buildContactCard(
                      context,
                      icon: Icons.phone,
                      title: 'Phone Support',
                      subtitle: '+*********** 000',
                      description: 'Call us during business hours (9 AM - 6 PM EAT)',
                      onTap: () => _launchPhone(),
                    ),
                    const SizedBox(height: 12),
                    _buildContactCard(
                      context,
                      icon: Icons.location_on,
                      title: 'Office Location',
                      subtitle: 'Nairobi, Kenya',
                      description: 'Visit our office for in-person support',
                      onTap: () => _launchMaps(),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Support Hours',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text('Monday - Friday: 9:00 AM - 6:00 PM (EAT)'),
                          const Text('Saturday: 10:00 AM - 4:00 PM (EAT)'),
                          const Text('Sunday: Closed'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // URL Launcher Methods
  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Support Request - Residence Hub',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+254700000000');

    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _launchMaps() async {
    final Uri mapsUri = Uri.parse('https://maps.google.com/?q=Nairobi,Kenya');

    if (await canLaunchUrl(mapsUri)) {
      await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
    }
  }

  // FAQ Method
  void _showFAQ(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.help_outline,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Frequently Asked Questions',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      tooltip: 'Close',
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    _buildFAQItem(
                      context,
                      'How do I reset my password?',
                      'Go to the login screen and tap "Forgot Password?" to receive a reset link via email.',
                    ),
                    _buildFAQItem(
                      context,
                      'How do I update my profile information?',
                      'Navigate to Profile from the main menu and tap the edit button to update your information.',
                    ),
                    _buildFAQItem(
                      context,
                      'How do I view my bills?',
                      'Go to the Bills section from the main menu to view all your bills, payments, and transaction history.',
                    ),
                    _buildFAQItem(
                      context,
                      'How do I change my app settings?',
                      'Access Settings from the main menu to customize your preferences, currency, and theme.',
                    ),
                    _buildFAQItem(
                      context,
                      'How do I contact support?',
                      'You can contact support through the Contact Support section in Settings, via <NAME_EMAIL>, or by calling +*********** 000.',
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFAQItem(BuildContext context, String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          question,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Privacy Policy Method
  void _showPrivacyPolicy(BuildContext context) {
    _showContentModal(
      context,
      'Privacy Policy',
      Icons.privacy_tip_outlined,
      _getPrivacyPolicyContent(),
    );
  }

  // Terms & Conditions Method
  void _showTermsConditions(BuildContext context) {
    _showContentModal(
      context,
      'Terms & Conditions',
      Icons.description_outlined,
      _getTermsConditionsContent(),
    );
  }

  // Changelog Method
  void _showChangelog(BuildContext context) {
    _showContentModal(
      context,
      'Changelog',
      Icons.update,
      _getChangelogContent(),
    );
  }

  void _showContentModal(BuildContext context, String title, IconData icon, String content) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      icon,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      tooltip: 'Close',
                    ),
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildModernContent(context, title, content),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernContent(BuildContext context, String title, String content) {
    if (title == 'Privacy Policy') {
      return _buildPrivacyPolicyContent(context);
    } else if (title == 'Terms & Conditions') {
      return _buildTermsConditionsContent(context);
    } else {
      // For changelog and other content, use the original format
      return Text(
        content,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          height: 1.5,
        ),
      );
    }
  }

  Widget _buildPrivacyPolicyContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLastUpdatedBadge(context),
        const SizedBox(height: 24),

        _buildContentSection(
          context,
          'Introduction',
          Icons.info_outline,
          'Welcome to Residence Hub. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application.',
        ),

        _buildContentSection(
          context,
          'Information We Collect',
          Icons.data_usage,
          'We may collect information about you in a variety of ways:',
          bulletPoints: [
            'Personal Data: Personally identifiable information, such as your name, email address, phone number, and demographic information that you voluntarily give to us when you register with the App.',
            'Financial Data: Financial information, such as data related to your payment method and billing information when you make payments through the App.',
            'Data from Social Networks: User information from social networking sites, if you connect your account to such social networks.',
          ],
        ),

        _buildContentSection(
          context,
          'Use of Your Information',
          Icons.settings,
          'Having accurate information about you permits us to provide you with a smooth, efficient, and customized experience. Specifically, we may use information collected about you via the App to:',
          bulletPoints: [
            'Create and manage your account',
            'Process your transactions and send you related information',
            'Send you administrative information',
            'Fulfill and manage purchases, orders, payments, and other transactions',
            'Generate a personal profile about you to make future visits to the App more personalized',
            'Increase the efficiency and operation of the App',
          ],
        ),

        _buildContentSection(
          context,
          'Disclosure of Your Information',
          Icons.share,
          'We may share information we have collected about you in certain situations:',
          bulletPoints: [
            'By Law or to Protect Rights: If we believe the release of information about you is necessary to respond to legal process, to investigate or remedy potential violations of our policies, or to protect the rights, property, and safety of others.',
            'Business Transfers: We may share or transfer your information in connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business to another company.',
          ],
        ),

        _buildContentSection(
          context,
          'Security of Your Information',
          Icons.security,
          'We use administrative, technical, and physical security measures to help protect your personal information. While we have taken reasonable steps to secure the personal information you provide to us, please be aware that despite our efforts, no security measures are perfect or impenetrable.',
        ),

        _buildContactSection(context),
      ],
    );
  }

  Widget _buildTermsConditionsContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLastUpdatedBadge(context),
        const SizedBox(height: 24),

        _buildContentSection(
          context,
          'Agreement to Terms',
          Icons.handshake,
          'By accessing and using Residence Hub, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
        ),

        _buildContentSection(
          context,
          'Description of Service',
          Icons.home_work,
          'Residence Hub is a mobile application designed to help tenants manage their housing information, bills, and communicate with property managers. The service includes features such as:',
          bulletPoints: [
            'Housing and room details management',
            'Bill tracking and payment processing',
            'Utility bill management',
            'Profile and settings management',
            'Communication tools',
          ],
        ),

        _buildContentSection(
          context,
          'User Accounts',
          Icons.account_circle,
          'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.',
        ),

        _buildContentSection(
          context,
          'Acceptable Use',
          Icons.rule,
          'You may use our service for lawful purposes only. You agree not to use the service:',
          bulletPoints: [
            'In any way that violates any applicable federal, state, local, or international law or regulation',
            'To transmit, or procure the sending of, any advertising or promotional material, or any other form of similar solicitation',
            'To impersonate or attempt to impersonate the Company, a Company employee, another user, or any other person or entity',
            'To engage in any other conduct that restricts or inhibits anyone\'s use or enjoyment of the service',
          ],
        ),

        _buildContentSection(
          context,
          'Payment Terms',
          Icons.payment,
          'If you make payments through the App, you agree to provide current, complete, and accurate purchase and account information. You agree to promptly update your account and other information so that we can complete your transactions and contact you as needed.',
        ),

        _buildContentSection(
          context,
          'Intellectual Property Rights',
          Icons.copyright,
          'The Service and its original content, features, and functionality are and will remain the exclusive property of Residence Hub and its licensors. The Service is protected by copyright, trademark, and other laws.',
        ),

        _buildContentSection(
          context,
          'Termination',
          Icons.cancel,
          'We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.',
        ),

        _buildContactSection(context),
      ],
    );
  }

  String _getPrivacyPolicyContent() {
    return '''Last updated: ${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}-${DateTime.now().day.toString().padLeft(2, '0')}

INTRODUCTION
Welcome to Residence Hub. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application.

INFORMATION WE COLLECT
We may collect information about you in a variety of ways:

• Personal Data: Personally identifiable information, such as your name, email address, phone number, and demographic information that you voluntarily give to us when you register with the App.

• Financial Data: Financial information, such as data related to your payment method and billing information when you make payments through the App.

• Data from Social Networks: User information from social networking sites, if you connect your account to such social networks.

USE OF YOUR INFORMATION
Having accurate information about you permits us to provide you with a smooth, efficient, and customized experience. Specifically, we may use information collected about you via the App to:

• Create and manage your account
• Process your transactions and send you related information
• Send you administrative information
• Fulfill and manage purchases, orders, payments, and other transactions
• Generate a personal profile about you to make future visits to the App more personalized
• Increase the efficiency and operation of the App

DISCLOSURE OF YOUR INFORMATION
We may share information we have collected about you in certain situations:

• By Law or to Protect Rights: If we believe the release of information about you is necessary to respond to legal process, to investigate or remedy potential violations of our policies, or to protect the rights, property, and safety of others.

• Business Transfers: We may share or transfer your information in connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business to another company.

SECURITY OF YOUR INFORMATION
We use administrative, technical, and physical security measures to help protect your personal information. While we have taken reasonable steps to secure the personal information you provide to us, please be aware that despite our efforts, no security measures are perfect or impenetrable.

CONTACT US
If you have questions or comments about this Privacy Policy, please contact us at:

Email: <EMAIL>
Phone: +*********** 000
Address: Nairobi, Kenya''';
  }

  Widget _buildLastUpdatedBadge(BuildContext context) {
    final now = DateTime.now();
    final dateStr = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.update,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 6),
          Text(
            'Last updated: $dateStr',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection(
    BuildContext context,
    String title,
    IconData icon,
    String description, {
    List<String>? bulletPoints,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                  color: Colors.grey[700],
                ),
              ),
              if (bulletPoints != null) ...[
                const SizedBox(height: 12),
                ...bulletPoints.map((point) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 6),
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          point,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            height: 1.4,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.contact_support,
                      size: 20,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Contact Information',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildContactItem(context, Icons.email, 'Email', '<EMAIL>'),
              const SizedBox(height: 8),
              _buildContactItem(context, Icons.phone, 'Phone', '+*********** 000'),
              const SizedBox(height: 8),
              _buildContactItem(context, Icons.location_on, 'Address', 'Nairobi, Kenya'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactItem(BuildContext context, IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  String _getTermsConditionsContent() {
    return '''Last updated: ${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}-${DateTime.now().day.toString().padLeft(2, '0')}

AGREEMENT TO TERMS
By accessing and using Residence Hub, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.

DESCRIPTION OF SERVICE
Residence Hub is a mobile application designed to help tenants manage their housing information, bills, and communicate with property managers. The service includes features such as:

• Housing and room details management
• Bill tracking and payment processing
• Utility bill management
• Profile and settings management
• Communication tools

USER ACCOUNTS
When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.

ACCEPTABLE USE
You may use our service for lawful purposes only. You agree not to use the service:

• In any way that violates any applicable federal, state, local, or international law or regulation
• To transmit, or procure the sending of, any advertising or promotional material, or any other form of similar solicitation
• To impersonate or attempt to impersonate the Company, a Company employee, another user, or any other person or entity
• To engage in any other conduct that restricts or inhibits anyone's use or enjoyment of the service

PAYMENT TERMS
If you make payments through the App, you agree to provide current, complete, and accurate purchase and account information. You agree to promptly update your account and other information so that we can complete your transactions and contact you as needed.

INTELLECTUAL PROPERTY RIGHTS
The Service and its original content, features, and functionality are and will remain the exclusive property of Residence Hub and its licensors. The Service is protected by copyright, trademark, and other laws.

TERMINATION
We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.

CONTACT INFORMATION
If you have any questions about these Terms and Conditions, please contact us at:

Email: <EMAIL>
Phone: +*********** 000
Address: Nairobi, Kenya''';
  }

  String _getChangelogContent() {
    return '''Version 1.0.0 (Latest)
• Initial release of Residence Hub
• User authentication and profile management
• Housing and room details tracking
• Bill management and payment processing
• Utility bills tracking
• Settings and preferences
• Dark/Light theme support
• Multi-currency support
• Remember me functionality
• Privacy policy and terms integration
• Modern settings interface with integrated support

Version 0.9.0 (Beta)
• Beta testing phase
• Core functionality implementation
• User interface refinements
• Security enhancements
• Bug fixes and performance improvements

Version 0.8.0 (Alpha)
• Alpha testing phase
• Basic authentication system
• Initial UI/UX design
• Database schema implementation
• Core features development

UPCOMING FEATURES
• Push notifications for bills and updates
• In-app messaging with property managers
• Document storage and management
• Maintenance request tracking
• Community features for tenants
• Enhanced reporting and analytics''';
  }
}
