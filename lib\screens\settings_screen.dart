import 'package:flutter/material.dart';
import 'package:currency_picker/currency_picker.dart';
import '../models/user_settings.dart';
import '../services/auth_service.dart';
import '../services/settings_service.dart';
import '../theme/index.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AuthService _authService = AuthService();
  final SettingsService _settingsService = SettingsService();

  bool _isLoading = true;
  UserSettings? _userSettings;
  String? _selectedCurrency;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        final settings = await _settingsService.getUserSettings(userId);

        if (!mounted) return;

        setState(() {
          _userSettings = settings;
          _selectedCurrency = settings.currencyCode;
        });
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading settings: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateCurrency(String currencyCode) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        await _settingsService.updateCurrencyCode(userId, currencyCode);

        if (!mounted) return;

        setState(() {
          _selectedCurrency = currencyCode;
          if (_userSettings != null) {
            _userSettings = _userSettings!.copyWith(currencyCode: currencyCode);
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Currency updated successfully')),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error updating currency: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showCurrencyPicker() {
    showCurrencyPicker(
      context: context,
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
      favorite: ['KES', 'USD', 'EUR', 'GBP'],
      onSelect: (Currency currency) {
        _updateCurrency(currency.code);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: AppThemeConstants.screenPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Currency Settings',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Divider(),
                          const SizedBox(height: 16),
                          ListTile(
                            title: const Text('Currency'),
                            subtitle: Text(
                              _selectedCurrency != null
                                  ? 'Current: $_selectedCurrency'
                                  : 'Select a currency',
                            ),
                            trailing: _selectedCurrency != null
                                ? CircleAvatar(
                                    backgroundColor: Theme.of(
                                      context,
                                    ).colorScheme.primaryContainer,
                                    child: Text(
                                      _selectedCurrency!,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  )
                                : null,
                            onTap: _showCurrencyPicker,
                          ),
                          const SizedBox(height: 8),
                          if (_selectedCurrency != null)
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Text(
                                'Symbol: ${_getCurrencySymbol(_selectedCurrency!)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _showCurrencyPicker,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                              child: const Text('Change Currency'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  String _getCurrencySymbol(String currencyCode) {
    try {
      final currency = CurrencyService().findByCode(currencyCode);
      return currency?.symbol ?? currencyCode;
    } catch (e) {
      return currencyCode;
    }
  }
}
