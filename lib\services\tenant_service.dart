import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/tenant_housing.dart';

class TenantService {
  final _supabase = Supabase.instance.client;

  /// Get tenant details by user ID
  Future<Map<String, dynamic>?> getTenantDetails(String? userId) async {
    if (userId == null) {
      return null;
    }

    try {
      // First try to get tenant data by matching auth user ID
      final response = await _supabase
          .from('tenants')
          .select('*, rooms(*, properties(*))')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        return response;
      }

      // If no tenant found by user_id, try to get by notes field (legacy approach)
      final legacyResponse = await _supabase
          .from('tenants')
          .select('*, rooms(*, properties(*))')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      if (legacyResponse != null) {
        return legacyResponse;
      }

      // If no tenant found by auth link, try to get profile data
      final profileResponse = await _supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

      return profileResponse;
    } catch (e) {
      debugPrint('Error fetching tenant details: $e');
      return null;
    }
  }

  /// Get property details by room ID
  Future<Map<String, dynamic>?> getPropertyDetails(String? roomId) async {
    if (roomId == null) return null;

    try {
      final response = await _supabase
          .from('rooms')
          .select('*, properties(*)')
          .eq('id', roomId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error fetching property details: $e');
      return null;
    }
  }

  /// Get tenant's full housing details including room, property, and amenities
  Future<TenantHousing?> getTenantHousingDetails(String? userId) async {
    if (userId == null) {
      return null;
    }

    try {
      debugPrint('TENANT_SERVICE: Fetching housing details for user: $userId');
      
      // First try to get tenant data by matching auth user ID with inner joins
      final tenantResponse = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms!inner(
              *,
              properties!inner(*),
              room_amenities(
                *,
                amenities(*)
              )
            )
          ''')
          .eq('user_id', userId)
          .maybeSingle();

      debugPrint('TENANT_SERVICE: Tenant response with inner join: $tenantResponse');

      if (tenantResponse != null) {
        final housingDetails = TenantHousing.fromJson(tenantResponse);
        debugPrint('TENANT_SERVICE: Parsed housing details: $housingDetails');
        debugPrint('TENANT_SERVICE: Room details: ${housingDetails.room}');
        debugPrint('TENANT_SERVICE: Property details: ${housingDetails.room?.property}');
        return housingDetails;
      }

      // If no tenant found by user_id, try legacy approach with notes field
      final legacyResponse = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms!inner(
              *,
              properties!inner(*),
              room_amenities(
                *,
                amenities(*)
              )
            )
          ''')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      debugPrint('TENANT_SERVICE: Legacy response with inner join: $legacyResponse');

      if (legacyResponse != null) {
        return TenantHousing.fromJson(legacyResponse);
      }

      // If the inner join didn't work, try with regular join in case tenant exists but may not have a room
      final tenantResponseFallback = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms(
              *,
              properties(*),
              room_amenities(
                *,
                amenities(*)
              )
            )
          ''')
          .eq('user_id', userId)
          .maybeSingle();

      debugPrint('TENANT_SERVICE: Tenant fallback response: $tenantResponseFallback');

      if (tenantResponseFallback != null) {
        return TenantHousing.fromJson(tenantResponseFallback);
      }

      // Try legacy approach with regular join as final fallback
      final legacyFallback = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms(
              *,
              properties(*),
              room_amenities(
                *,
                amenities(*)
              )
            )
          ''')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      debugPrint('TENANT_SERVICE: Legacy fallback response: $legacyFallback');

      if (legacyFallback != null) {
        return TenantHousing.fromJson(legacyFallback);
      }      
      
      debugPrint('TENANT_SERVICE: No tenant housing details found for user: $userId');
      return null;
    } catch (e) {
      debugPrint('TENANT_SERVICE: Error fetching tenant housing details: $e');
      return null;
    }
  }

  /// Get tenant details by tenant ID directly (useful for receipt generation)
  Future<Map<String, dynamic>?> getTenantById(String? tenantId) async {
    if (tenantId == null) {
      return null;
    }

    try {
      // Fetch tenant with room and property details
      final response = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms(
              *,
              properties(*)
            )
          ''')
          .eq('id', tenantId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error fetching tenant by ID: $e');
      return null;
    }
  }

  /// Get room details with amenities
  Future<Room?> getRoomDetails(String? roomId) async {
    if (roomId == null) return null;

    try {
      final response = await _supabase
          .from('rooms_with_amenities') // Using the view created in migrations
          .select('*')
          .eq('id', roomId)
          .maybeSingle();

      if (response != null) {
        return Room.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching room details: $e');
      return null;
    }
  }

  /// Get utility bills for tenant's property
  Future<List<Map<String, dynamic>>> getPropertyUtilityBills(String? propertyId) async {
    if (propertyId == null) return [];

    try {
      final response = await _supabase
          .from('utility_bills')
          .select('*')
          .eq('property_id', propertyId)
          .order('name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error fetching utility bills: $e');
      return [];
    }
  }
}

