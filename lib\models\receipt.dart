import 'package:uuid/uuid.dart';

/// Status of a payment receipt
enum ReceiptStatus { pending, verified, rejected }

/// Model representing a payment receipt
class Receipt {
  final String id;
  final String paymentId;
  final List<String> billIds;
  final String tenantId;
  final String? tenantName;
  final String? propertyName;
  final String? roomName;
  final double amount;
  final DateTime paymentDate;
  final DateTime printedDate;
  final ReceiptStatus status;
  final String? method;
  final String receiptReference;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? verifiedBy;

  Receipt({
    String? id,
    required this.paymentId,
    required this.billIds,
    required this.tenantId,
    this.tenantName,
    this.propertyName,
    this.roomName,
    required this.amount,
    required this.paymentDate,
    DateTime? printedDate,
    this.status = ReceiptStatus.verified,
    this.method,
    String? receiptReference,
    this.notes,
    DateTime? createdAt,
    this.updatedAt,
    this.verifiedBy,
  }) : id = id ?? const Uuid().v4(),
       printedDate = printedDate ?? DateTime.now(),
       receiptReference = receiptReference ?? 'RCP-${DateTime.now().millisecondsSinceEpoch}',
       createdAt = createdAt ?? DateTime.now();

  /// Factory constructor from JSON
  factory Receipt.fromJson(Map<String, dynamic> json) {
    List<String> billIds = [];
    if (json['bill_ids'] != null) {
      // Handle different bill_ids formats (array or string)
      if (json['bill_ids'] is List) {
        billIds = List<String>.from(json['bill_ids']);
      } else if (json['bill_ids'] is String) {
        // Parse PostgreSQL array format {uuid,uuid}
        final billIdsString = json['bill_ids'] as String;
        if (billIdsString.startsWith('{') && billIdsString.endsWith('}')) {
          final content = billIdsString.substring(1, billIdsString.length - 1);
          billIds = content.split(',');
        }
      }
    }

    return Receipt(
      id: json['id'],
      paymentId: json['payment_id'],
      billIds: billIds,
      tenantId: json['tenant_id'],
      tenantName: json['tenant_name'],
      propertyName: json['property_name'],
      roomName: json['room_name'],
      amount: (json['amount'] ?? 0).toDouble(),
      paymentDate: json['payment_date'] != null
          ? DateTime.parse(json['payment_date'])
          : DateTime.now(),
      printedDate: json['printed_date'] != null
          ? DateTime.parse(json['printed_date'])
          : DateTime.now(),
      status: _parseStatus(json['status']),
      method: json['method'],
      receiptReference: json['receipt_reference'],
      notes: json['notes'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      verifiedBy: json['verified_by'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'payment_id': paymentId,
      'bill_ids': billIds,
      'tenant_id': tenantId,
      'tenant_name': tenantName,
      'property_name': propertyName,
      'room_name': roomName,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'printed_date': printedDate.toIso8601String(),
      'status': status.name,
      'method': method,
      'receipt_reference': receiptReference,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'verified_by': verifiedBy,
    };
  }

  /// Parse status from string
  static ReceiptStatus _parseStatus(String? status) {
    if (status == null) return ReceiptStatus.pending;
    switch (status.toLowerCase()) {
      case 'verified':
        return ReceiptStatus.verified;
      case 'rejected':
        return ReceiptStatus.rejected;
      case 'pending':
      default:
        return ReceiptStatus.pending;
    }
  }

  /// Create a copy of this receipt with some fields changed
  Receipt copyWith({
    String? id,
    String? paymentId,
    List<String>? billIds,
    String? tenantId,
    String? tenantName,
    String? propertyName,
    String? roomName,
    double? amount,
    DateTime? paymentDate,
    DateTime? printedDate,
    ReceiptStatus? status,
    String? method,
    String? receiptReference,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? verifiedBy,
  }) {
    return Receipt(
      id: id ?? this.id,
      paymentId: paymentId ?? this.paymentId,
      billIds: billIds ?? this.billIds,
      tenantId: tenantId ?? this.tenantId,
      tenantName: tenantName ?? this.tenantName,
      propertyName: propertyName ?? this.propertyName,
      roomName: roomName ?? this.roomName,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      printedDate: printedDate ?? this.printedDate,
      status: status ?? this.status,
      method: method ?? this.method,
      receiptReference: receiptReference ?? this.receiptReference,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
    );
  }
} 