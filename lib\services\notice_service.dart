import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notice.dart';

class NoticeService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get all notices for the current tenant across all their properties
  Future<List<Notice>> getTenantNotices() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Fetching notices for user: $userId');

      // Use the new function that gets notices from all properties where user is a tenant
      final response = await _supabase.rpc('get_tenant_notices');

      debugPrint('NOTICE_SERVICE: Raw response: $response');

      if (response == null) {
        return [];
      }

      final List<dynamic> noticesData = response is List ? response : [response];
      final notices = noticesData.map((json) => Notice.fromJson(json)).toList();

      debugPrint('NOTICE_SERVICE: Parsed ${notices.length} notices');
      return notices;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching tenant notices: $e');
      rethrow;
    }
  }

  /// Get urgent notices for the current user
  Future<List<Notice>> getUrgentNotices() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Fetching urgent notices for user: $userId');

      final response = await _supabase.rpc('get_urgent_notices');

      debugPrint('NOTICE_SERVICE: Urgent notices response: $response');

      if (response == null) {
        return [];
      }

      final List<dynamic> noticesData = response is List ? response : [response];
      final notices = noticesData.map((json) => Notice.fromJson(json)).toList();
      
      debugPrint('NOTICE_SERVICE: Parsed ${notices.length} urgent notices');
      return notices;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching urgent notices: $e');
      rethrow;
    }
  }

  /// Get a specific notice by ID
  Future<Notice?> getNoticeById(String noticeId) async {
    try {
      final response = await _supabase
          .from('active_community_notices')
          .select()
          .eq('id', noticeId)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return Notice.fromJson(response);
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notice by ID: $e');
      rethrow;
    }
  }

  /// Get notices filtered by type
  Future<List<Notice>> getNoticesByType(String type) async {
    try {
      final notices = await getTenantNotices();
      return notices.where((notice) => notice.type == type).toList();
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notices by type: $e');
      rethrow;
    }
  }

  /// Get notices filtered by priority
  Future<List<Notice>> getNoticesByPriority(String priority) async {
    try {
      final notices = await getTenantNotices();
      return notices.where((notice) => notice.priority == priority).toList();
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notices by priority: $e');
      rethrow;
    }
  }



  /// Get notices summary for dashboard
  Future<Map<String, dynamic>> getNoticesSummary() async {
    try {
      final notices = await getTenantNotices();
      final urgentNotices = notices.where((n) => n.isUrgent).length;
      final highPriorityNotices = notices.where((n) => n.isHigh).length;
      final totalNotices = notices.length;
      final unreadNotices = notices.length; // For now, all notices are considered unread

      return {
        'total': totalNotices,
        'urgent': urgentNotices,
        'high_priority': highPriorityNotices,
        'unread': unreadNotices,
      };
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error getting notices summary: $e');
      return {
        'total': 0,
        'urgent': 0,
        'high_priority': 0,
        'unread': 0,
      };
    }
  }

  /// Create a new community notice (Property managers only)
  Future<String> createNotice({
    required String title,
    required String content,
    required String propertyId,
    String type = 'general',
    String priority = 'medium',
    DateTime? expiresAt,
    List<String>? attachmentUrls,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Creating notice for property: $propertyId');

      final response = await _supabase.rpc('create_community_notice', params: {
        'notice_title': title,
        'notice_content': content,
        'notice_type': type,
        'notice_priority': priority,
        'target_property_id': propertyId,
        'notice_expires_at': expiresAt?.toIso8601String(),
        'notice_attachment_urls': attachmentUrls,
        'notice_metadata': metadata,
      });

      debugPrint('NOTICE_SERVICE: Created notice with ID: $response');
      return response as String;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error creating notice: $e');
      rethrow;
    }
  }

  /// Update an existing community notice (Property managers only)
  Future<bool> updateNotice({
    required String noticeId,
    String? title,
    String? content,
    String? type,
    String? priority,
    DateTime? expiresAt,
    List<String>? attachmentUrls,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Updating notice: $noticeId');

      final response = await _supabase.rpc('update_community_notice', params: {
        'notice_id': noticeId,
        'notice_title': title,
        'notice_content': content,
        'notice_type': type,
        'notice_priority': priority,
        'notice_expires_at': expiresAt?.toIso8601String(),
        'notice_attachment_urls': attachmentUrls,
        'notice_metadata': metadata,
      });

      debugPrint('NOTICE_SERVICE: Updated notice: $response');
      return response as bool;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error updating notice: $e');
      rethrow;
    }
  }

  /// Delete/deactivate a community notice (Property managers only)
  Future<bool> deleteNotice(String noticeId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Deleting notice: $noticeId');

      final response = await _supabase.rpc('delete_community_notice', params: {
        'notice_id': noticeId,
      });

      debugPrint('NOTICE_SERVICE: Deleted notice: $response');
      return response as bool;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error deleting notice: $e');
      rethrow;
    }
  }

  /// Get notices for a specific property (Property managers only)
  Future<List<Notice>> getPropertyNotices(String propertyId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Fetching notices for property: $propertyId');

      final response = await _supabase.rpc('get_property_notices', params: {
        'property_uuid': propertyId,
      });

      debugPrint('NOTICE_SERVICE: Raw property notices response: $response');

      if (response == null) {
        return [];
      }

      final List<dynamic> noticesData = response is List ? response : [response];
      final notices = noticesData.map((json) => Notice.fromJson(json)).toList();

      debugPrint('NOTICE_SERVICE: Parsed ${notices.length} property notices');
      return notices;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching property notices: $e');
      rethrow;
    }
  }
}
