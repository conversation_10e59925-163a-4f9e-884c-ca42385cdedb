import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notice.dart';

class NoticeService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get all notices for the current tenant's property
  Future<List<Notice>> getTenantNotices() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Fetching notices for user: $userId');

      // Get notices for properties where the user is a tenant
      final response = await _supabase
          .rpc('get_property_notices', params: {
            'property_uuid': await _getTenantPropertyId(userId),
          });

      debugPrint('NOTICE_SERVICE: Raw response: $response');

      if (response == null) {
        return [];
      }

      final List<dynamic> noticesData = response is List ? response : [response];
      final notices = noticesData.map((json) => Notice.fromJson(json)).toList();
      
      debugPrint('NOTICE_SERVICE: Parsed ${notices.length} notices');
      return notices;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching tenant notices: $e');
      rethrow;
    }
  }

  /// Get urgent notices for the current user
  Future<List<Notice>> getUrgentNotices() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('NOTICE_SERVICE: Fetching urgent notices for user: $userId');

      final response = await _supabase.rpc('get_urgent_notices');

      debugPrint('NOTICE_SERVICE: Urgent notices response: $response');

      if (response == null) {
        return [];
      }

      final List<dynamic> noticesData = response is List ? response : [response];
      final notices = noticesData.map((json) => Notice.fromJson(json)).toList();
      
      debugPrint('NOTICE_SERVICE: Parsed ${notices.length} urgent notices');
      return notices;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching urgent notices: $e');
      rethrow;
    }
  }

  /// Get a specific notice by ID
  Future<Notice?> getNoticeById(String noticeId) async {
    try {
      final response = await _supabase
          .from('active_community_notices')
          .select()
          .eq('id', noticeId)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return Notice.fromJson(response);
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notice by ID: $e');
      rethrow;
    }
  }

  /// Get notices filtered by type
  Future<List<Notice>> getNoticesByType(String type) async {
    try {
      final notices = await getTenantNotices();
      return notices.where((notice) => notice.type == type).toList();
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notices by type: $e');
      rethrow;
    }
  }

  /// Get notices filtered by priority
  Future<List<Notice>> getNoticesByPriority(String priority) async {
    try {
      final notices = await getTenantNotices();
      return notices.where((notice) => notice.priority == priority).toList();
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error fetching notices by priority: $e');
      rethrow;
    }
  }

  /// Helper method to get the property ID for the current tenant
  Future<String> _getTenantPropertyId(String userId) async {
    try {
      // Get tenant data to find their property
      final tenantResponse = await _supabase
          .from('tenants')
          .select('rooms!inner(property_id)')
          .eq('user_id', userId)
          .maybeSingle();

      if (tenantResponse == null) {
        throw Exception('Tenant not found for user');
      }

      final propertyId = tenantResponse['rooms']['property_id'] as String;
      debugPrint('NOTICE_SERVICE: Found property ID: $propertyId');
      return propertyId;
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error getting tenant property ID: $e');
      rethrow;
    }
  }

  /// Get notices summary for dashboard
  Future<Map<String, dynamic>> getNoticesSummary() async {
    try {
      final notices = await getTenantNotices();
      final urgentNotices = notices.where((n) => n.isUrgent).length;
      final highPriorityNotices = notices.where((n) => n.isHigh).length;
      final totalNotices = notices.length;
      final unreadNotices = notices.length; // For now, all notices are considered unread

      return {
        'total': totalNotices,
        'urgent': urgentNotices,
        'high_priority': highPriorityNotices,
        'unread': unreadNotices,
      };
    } catch (e) {
      debugPrint('NOTICE_SERVICE: Error getting notices summary: $e');
      return {
        'total': 0,
        'urgent': 0,
        'high_priority': 0,
        'unread': 0,
      };
    }
  }
}
