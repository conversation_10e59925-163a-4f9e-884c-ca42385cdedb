# Network Logs Capture - Bills Issue Reproduction

## Test Session: Tenant Bills Query Issue
**Date**: 2025-07-02
**Purpose**: Capture network logs when reproducing the empty bills query issue

## Setup
- App running in debug mode with Supabase logging enabled
- User: Tenant with known bills
- Target: Observe Supabase query for `tenant_bills_view`

## Expected Query
```sql
SELECT * FROM tenant_bills_view WHERE tenant_id = '[tenant_id]' ORDER BY due_date ASC;
```

## Actual Network Logs

### 1. Authentication Request
```
[To be captured during testing]
```

### 2. Bills Query Request
```
[To be captured during testing]
```

### 3. Response Data
```
[To be captured during testing]
```

## Issues Found
- [ ] Query returns empty set
- [ ] Query returns error
- [ ] RLS policy blocking access
- [ ] View missing required columns
- [ ] Tenant ID mapping issue

## Root Cause Analysis
[To be filled during investigation]

## Request/Response Payloads
[To be captured and saved for reference]
