import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/tenant_housing.dart';
import '../utils/currency_formatter.dart';
import '../theme/index.dart';

class WelcomeCard extends StatelessWidget {
  final String userName;
  final DateTime currentTime;
  final TenantHousing? housingDetails;

  const WelcomeCard({
    super.key,
    required this.userName,
    required this.currentTime,
    this.housingDetails,
  });

  @override
  Widget build(BuildContext context) {
    // Debug logging
    debugPrint('WELCOME_CARD: housingDetails: $housingDetails');
    debugPrint('WELCOME_CARD: housingDetails?.room: ${housingDetails?.room}');
    debugPrint('WELCOME_CARD: housingDetails?.room?.property: ${housingDetails?.room?.property}');
    
    // Get property and room details from housing details if available
    String propertyName = 'Your Property';
    String roomName = 'Your Room';
    String rentAmount = '';

    if (housingDetails?.room != null) {
      final housing = housingDetails!;
      debugPrint('WELCOME_CARD: Housing object is not null');
      if (housing.room?.property != null) {
        propertyName = housing.room!.property!.name;
        debugPrint('WELCOME_CARD: Property name set to: $propertyName');
      } else {
        debugPrint('WELCOME_CARD: Property is null');
      }
      if (housing.room != null) {
        roomName = housing.room!.name;
        rentAmount = CurrencyFormatter.format(
          context,
          housing.room!.rentalPrice,
        );
        debugPrint('WELCOME_CARD: Room name set to: $roomName, rent: $rentAmount');
      }
    } else {
      debugPrint('WELCOME_CARD: housingDetails or room is null');
    }

    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          AppThemeConstants.radiusL,
        ),
      ),
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Color(0xFF2A6FDB), // Bright blue color from the image
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Welcome back,',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          userName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        AppThemeConstants.radiusM,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          DateFormat(
                            'h:mm',
                          ).format(currentTime),
                          style: const TextStyle(
                            color: Color(0xFF2A6FDB),
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          DateFormat(
                            'a',
                          ).format(currentTime),
                          style: const TextStyle(
                            color: Color(0xFF2A6FDB),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                    AppThemeConstants.radiusM,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: const Color(0xFF2A6FDB),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat(
                        'EEEE, MMMM d, y',
                      ).format(currentTime),
                      style: const TextStyle(
                        color: Color(0xFF2A6FDB),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          AppThemeConstants.radiusM,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.apartment,
                            color: Color(0xFF2A6FDB),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Property',
                                  style: TextStyle(
                                    color: Colors.black54,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  propertyName,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          AppThemeConstants.radiusM,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.door_front_door,
                            color: Color(0xFF2A6FDB),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Room',
                                  style: TextStyle(
                                    color: Colors.black54,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  roomName,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              if (rentAmount.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(
                      AppThemeConstants.radiusM,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.payments_outlined,
                        color: Color(0xFF2A6FDB),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Monthly Rent',
                              style: TextStyle(
                                color: Colors.black54,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '$rentAmount / month',
                              style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // View Housing Details Button
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/housing',
                    );
                  },
                  icon: const Icon(
                    Icons.apartment,
                    color: Color(0xFF2A6FDB),
                  ),
                  label: const Text(
                    'View Housing Details',
                    style: TextStyle(
                      color: Color(0xFF2A6FDB),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppThemeConstants.radiusM,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}