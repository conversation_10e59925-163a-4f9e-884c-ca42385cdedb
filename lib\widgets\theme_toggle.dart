import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/theme_manager.dart';

/// A widget that allows users to toggle between light and dark themes
class ThemeToggle extends StatelessWidget {
  /// Creates a theme toggle widget
  const ThemeToggle({super.key});

  @override
  Widget build(BuildContext context) {
    final themeManager = Provider.of<ThemeManager>(context);
    final isDarkMode = themeManager.isDarkMode;

    return IconButton(
      icon: Icon(
        isDarkMode ? Icons.light_mode : Icons.dark_mode,
        color: isDarkMode ? Colors.amber : Colors.blueGrey,
      ),
      tooltip: isDarkMode ? 'Switch to light mode' : 'Switch to dark mode',
      onPressed: () {
        themeManager.toggleTheme();
      },
    );
  }
}

/// A more advanced theme selector that allows users to choose between light, dark, and system themes
class ThemeSelector extends StatelessWidget {
  /// Creates a theme selector widget
  const ThemeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final themeManager = Provider.of<ThemeManager>(context);

    return PopupMenuButton<ThemeMode>(
      icon: Icon(
        themeManager.themeMode == ThemeMode.system
            ? Icons.brightness_auto
            : themeManager.themeMode == ThemeMode.light
            ? Icons.light_mode
            : Icons.dark_mode,
      ),
      tooltip: 'Select theme',
      onSelected: (ThemeMode mode) {
        themeManager.setThemeMode(mode);
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: ThemeMode.light,
          child: Row(
            children: [
              Icon(Icons.light_mode, color: Colors.amber),
              SizedBox(width: 8),
              Text('Light'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: ThemeMode.dark,
          child: Row(
            children: [
              Icon(Icons.dark_mode, color: Colors.blueGrey),
              SizedBox(width: 8),
              Text('Dark'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: ThemeMode.system,
          child: Row(
            children: [
              Icon(Icons.brightness_auto),
              SizedBox(width: 8),
              Text('System'),
            ],
          ),
        ),
      ],
    );
  }
}
