import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class CurrencyFormatter {
  /// Format a number with the user's preferred currency symbol
  static String format(BuildContext context, double amount) {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final symbol = settingsProvider.currencySymbol;

    // Create a formatter with the user's preferred currency symbol
    final formatter = NumberFormat.currency(symbol: symbol, decimalDigits: 2);

    return formatter.format(amount);
  }

  /// Format a number with a specific currency symbol
  static String formatWithSymbol(double amount, String symbol) {
    final formatter = NumberFormat.currency(symbol: symbol, decimalDigits: 2);

    return formatter.format(amount);
  }
}
