import 'package:flutter/material.dart';
import '../models/user_settings.dart';
import '../services/auth_service.dart';
import '../services/settings_service.dart';

class SettingsProvider extends ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  final AuthService _authService = AuthService();

  UserSettings? _userSettings;
  bool _isLoading = false;

  UserSettings? get userSettings => _userSettings;
  bool get isLoading => _isLoading;

  // Get currency symbol
  String get currencySymbol {
    if (_userSettings == null) return 'KSh';
    return _settingsService.getCurrencySymbol(_userSettings!.currencyCode);
  }

  // Initialize settings
  Future<void> loadSettings() async {
    if (!_authService.isLoggedIn) return;

    _isLoading = true;
    notifyListeners();

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        _userSettings = await _settingsService.getUserSettings(userId);
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update currency code
  Future<void> updateCurrencyCode(String currencyCode) async {
    if (!_authService.isLoggedIn || _userSettings == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      final userId = _authService.currentUser?.id;
      if (userId != null) {
        await _settingsService.updateCurrencyCode(userId, currencyCode);
        _userSettings = _userSettings!.copyWith(currencyCode: currencyCode);
      }
    } catch (e) {
      debugPrint('Error updating currency code: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
