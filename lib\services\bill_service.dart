import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/bill.dart';

class BillService {
  final _supabase = Supabase.instance.client;

  /// Get all bills for the currently logged-in tenant
  Future<List<Bill>> getTenantBills({String? tenantId}) async {
    try {
      debugPrint('=== FETCHING TENANT BILLS ===');
      final userId = _supabase.auth.currentUser?.id;
      debugPrint('Current user ID: $userId');
      
      if (userId == null) {
        debugPrint('ERROR: User not authenticated');
        throw Exception('User not authenticated');
      }

      // First, get the tenant ID for the current user if not provided
      String? currentTenantId = tenantId;
      if (currentTenantId == null) {
        debugPrint('Getting tenant ID for user: $userId');
        currentTenantId = await _getTenantIdByUserId(userId);
        debugPrint('Found tenant ID: $currentTenantId');
        if (currentTenantId == null) {
          debugPrint('ERROR: No tenant record found for user');
          return []; // No tenant record found
        }
      }

      // Check if the tenant has any bill-tenant relations
      debugPrint('Checking if bill-tenant relations exist for tenant: $currentTenantId');
      final billTenants = await _supabase
          .from('bill_tenants')
          .select('bill_id')
          .eq('tenant_id', currentTenantId);
      
      if (billTenants.isEmpty) {
        debugPrint('No bill-tenant relations found. Creating relations from bills table.');
        // Find bills that belong to this tenant but don't have relations
        final bills = await _supabase
            .from('bills')
            .select('id, amount')
            .eq('tenant_id', currentTenantId);
            
        if (bills.isNotEmpty) {
          debugPrint('Found ${bills.length} bills to create relations for.');
          
          // Create bill-tenant relations
          for (final bill in bills) {
            try {
              await _supabase.from('bill_tenants').insert({
                'bill_id': bill['id'],
                'tenant_id': currentTenantId,
                'split_amount': bill['amount'],
              });
              debugPrint('Created relation for bill: ${bill['id']}');
            } catch (e) {
              // Ignore duplicate key errors, just means relation already exists
              debugPrint('Error creating bill relation: $e');
            }
          }
        }
      }

      // Query bills using the tenant_bills_view which handles RLS
      debugPrint('Querying tenant_bills_view for tenant: $currentTenantId');
      debugPrint('Query: SELECT * FROM tenant_bills_view WHERE tenant_id = $currentTenantId ORDER BY due_date ASC');
      
      final response = await _supabase
          .from('tenant_bills_view')
          .select('*')
          .eq('tenant_id', currentTenantId)
          .order('due_date', ascending: true);
          
      debugPrint('Query response received: ${response.length} records');
      
      if (response.isEmpty) {
        debugPrint('No bills found in tenant_bills_view');
        return [];
      }
      
      // Print first record for debugging
      if (response.isNotEmpty) {
        debugPrint('First record sample: ${response[0]}');
      }

      final bills = <Bill>[];
      for (final billData in response) {
        try {
          // Convert the view data back to bill format
          final billJson = _convertViewToBillJson(billData);
          bills.add(Bill.fromJson(billJson));
        } catch (e) {
          debugPrint('Error parsing bill: $e');
          debugPrint('Problem bill data: $billData');
          // Continue with other bills even if one fails
        }
      }

      debugPrint('Successfully parsed ${bills.length} bills');
      return bills;
    } catch (e) {
      debugPrint('Error fetching tenant bills: $e');
      return [];
    }
  }

  /// Get bills by status for the current tenant
  Future<List<Bill>> getTenantBillsByStatus(BillStatus status, {String? tenantId}) async {
    try {
      final bills = await getTenantBills(tenantId: tenantId);
      return bills.where((bill) => bill.status == status).toList();
    } catch (e) {
      debugPrint('Error fetching bills by status: $e');
      return [];
    }
  }

  /// Get overdue bills for the current tenant
  Future<List<Bill>> getOverdueBills({String? tenantId}) async {
    try {
      final bills = await getTenantBills(tenantId: tenantId);
      return bills.where((bill) => bill.isOverdue).toList();
    } catch (e) {
      debugPrint('Error fetching overdue bills: $e');
      return [];
    }
  }

  /// Get upcoming bills (due within next 7 days)
  Future<List<Bill>> getUpcomingBills({String? tenantId, int days = 7}) async {
    try {
      final bills = await getTenantBills(tenantId: tenantId);
      final now = DateTime.now();
      final futureDate = now.add(Duration(days: days));
      
      return bills.where((bill) => 
        bill.status == BillStatus.pending &&
        bill.dueDate.isAfter(now) &&
        bill.dueDate.isBefore(futureDate)
      ).toList();
    } catch (e) {
      debugPrint('Error fetching upcoming bills: $e');
      return [];
    }
  }

  /// Get a specific bill by ID
  Future<Bill?> getBillById(String billId) async {
    try {
      debugPrint('Fetching bill by ID: $billId');
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final tenantId = await _getTenantIdByUserId(userId);
      if (tenantId == null) {
        debugPrint('No tenant record found for user: $userId');
        return null;
      }

      final response = await _supabase
          .from('tenant_bills_view')
          .select('*')
          .eq('tenant_id', tenantId)
          .eq('bill_id', billId)
          .maybeSingle();

      if (response != null) {
        debugPrint('Bill found: ${response['bill_title']}');
        final billJson = _convertViewToBillJson(response);
        return Bill.fromJson(billJson);
      }
      
      debugPrint('No bill found in tenant_bills_view, checking bills table');
      
      // If not found in view, try direct bills table as fallback
      final directBill = await _supabase
          .from('bills')
          .select('*')
          .eq('id', billId)
          .eq('tenant_id', tenantId)
          .maybeSingle();
          
      if (directBill != null) {
        debugPrint('Bill found in bills table: ${directBill['title']}');
        return Bill.fromJson(directBill);
      }
      
      debugPrint('No bill found with ID: $billId');
      return null;
    } catch (e) {
      debugPrint('Error fetching bill by ID: $e');
      return null;
    }
  }

  /// Get tenant's total outstanding amount
  Future<double> getTotalOutstandingAmount({String? tenantId}) async {
    try {
      final bills = await getTenantBills(tenantId: tenantId);
      double total = 0.0;
      
      for (final bill in bills) {
        if (bill.status != BillStatus.paid && bill.status != BillStatus.cancelled) {
          total += bill.remainingAmount;
        }
      }
      
      return total;
    } catch (e) {
      debugPrint('Error calculating total outstanding: $e');
      return 0.0;
    }
  }

  /// Get bills summary for dashboard
  Future<Map<String, dynamic>> getBillsSummary({String? tenantId}) async {
    try {
      final bills = await getTenantBills(tenantId: tenantId);
      
      final summary = <String, dynamic>{
        'total_bills': bills.length,
        'pending_bills': bills.where((b) => b.status == BillStatus.pending).length,
        'overdue_bills': bills.where((b) => b.isOverdue).length,
        'paid_bills': bills.where((b) => b.status == BillStatus.paid).length,
        'total_outstanding': 0.0,
        'overdue_amount': 0.0,
        'next_due_bill': null,
      };

      double totalOutstanding = 0.0;
      double overdueAmount = 0.0;
      Bill? nextDueBill;

      for (final bill in bills) {
        if (bill.status != BillStatus.paid && bill.status != BillStatus.cancelled) {
          totalOutstanding += bill.remainingAmount;
          
          if (bill.isOverdue) {
            overdueAmount += bill.remainingAmount;
          }
          
          // Find next due bill
          if (bill.status == BillStatus.pending && !bill.isOverdue) {
            if (nextDueBill == null || bill.dueDate.isBefore(nextDueBill.dueDate)) {
              nextDueBill = bill;
            }
          }
        }
      }

      summary['total_outstanding'] = totalOutstanding;
      summary['overdue_amount'] = overdueAmount;
      if (nextDueBill != null) {
        summary['next_due_bill'] = {
          'id': nextDueBill.id,
          'title': nextDueBill.title,
          'amount': nextDueBill.amount,
          'due_date': nextDueBill.dueDate.toIso8601String(),
          'days_until_due': nextDueBill.daysUntilDue,
        };
      }

      return summary;
    } catch (e) {
      debugPrint('Error getting bills summary: $e');
      return {
        'total_bills': 0,
        'pending_bills': 0,
        'overdue_bills': 0,
        'paid_bills': 0,
        'total_outstanding': 0.0,
        'overdue_amount': 0.0,
        'next_due_bill': null,
      };
    }
  }

  /// Get tenant ID by user ID
  Future<String?> _getTenantIdByUserId(String userId) async {
    try {
      debugPrint('Looking up tenant ID for user: $userId');
      
      // First try direct user_id match
      var response = await _supabase
          .from('tenants')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        debugPrint('Found tenant via user_id: ${response['id']}');
        return response['id'];
      }

      debugPrint('No tenant found via user_id, trying legacy approach with notes field');
      
      // Try legacy approach with notes field
      response = await _supabase
          .from('tenants')
          .select('id')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      if (response != null) {
        debugPrint('Found tenant via notes field: ${response['id']}');
        return response['id'];
      }

      debugPrint('No tenant found for user ID: $userId');
      return null;
    } catch (e) {
      debugPrint('Error getting tenant ID: $e');
      return null;
    }
  }

  /// Convert tenant_bills_view data to bill JSON format
  Map<String, dynamic> _convertViewToBillJson(Map<String, dynamic> viewData) {
    // Add debug logs for key fields to ensure data is available
    debugPrint('Converting view data to bill JSON for bill: ${viewData['bill_title']}');
    
    // Handle null values with safe defaults
    return {
      'id': viewData['bill_id'],
      'title': viewData['bill_title'] ?? 'Unnamed Bill',
      'description': viewData['bill_description'] ?? '',
      'amount': viewData['bill_amount'] ?? 0,
      'due_date': viewData['due_date'] ?? DateTime.now().toIso8601String(),
      'status': viewData['bill_status'] ?? 'pending',
      'type': viewData['bill_type'] ?? 'other',
      'recurrence': viewData['bill_recurrence'] ?? 'oneTime',
      'tenant_id': viewData['tenant_id'],
      'property_id': viewData['property_id'],
      'room_id': viewData['room_id'],
      'created_at': viewData['created_at'] ?? DateTime.now().toIso8601String(),
      'updated_at': viewData['updated_at'],
      'paid_at': viewData['paid_at'],
      'paid_amount': viewData['paid_amount'],
      'notes': viewData['notes'],
      'bill_number': viewData['bill_number'],
      'include_in_rent': viewData['include_in_rent'] ?? false,
      'utility_type': viewData['utility_type'],
      'previous_meter_reading': viewData['previous_meter_reading'],
      'current_meter_reading': viewData['current_meter_reading'],
      'unit_consumed': viewData['unit_consumed'],
      'rate_per_unit': viewData['rate_per_unit'],
      'bill_components': viewData['bill_components'],
    };
  }

  /// Get a payment by ID
  Future<Map<String, dynamic>?> getPaymentById(String paymentId) async {
    try {
      final response = await _supabase
          .from('tenant_payments_view')
          .select('*')
          .eq('payment_id', paymentId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error getting payment by ID: $e');
      return null;
    }
  }

  /// Get payments for a bill
  Future<List<Map<String, dynamic>>> getPaymentsForBill(String billId) async {
    try {
      final response = await _supabase
          .from('tenant_payments_view')
          .select('*')
          .filter('bill_ids', 'cs', '{$billId}')
          .order('payment_date', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting payments for bill: $e');
      return [];
    }
  }

  /// Get tenant payments
  Future<List<Map<String, dynamic>>> getTenantPayments({String? tenantId}) async {
    try {
      final String? currentTenantId = tenantId ?? await _getCurrentTenantId();
      if (currentTenantId == null) {
        return [];
      }

      final response = await _supabase
          .from('tenant_payments_view')
          .select('*')
          .eq('tenant_id', currentTenantId)
          .order('payment_date', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting tenant payments: $e');
      return [];
    }
  }

  /// Get current tenant ID from auth user
  Future<String?> _getCurrentTenantId() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        return null;
      }
      
      // First try direct user_id match
      var response = await _supabase
          .from('tenants')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        return response['id'];
      }
      
      // Try legacy approach with notes field
      response = await _supabase
          .from('tenants')
          .select('id')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      if (response != null) {
        return response['id'];
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting current tenant ID: $e');
      return null;
    }
  }

  /// Get recent payments
  Future<List<Map<String, dynamic>>> getRecentPayments({int limit = 5, String? tenantId}) async {
    try {
      final String? currentTenantId = tenantId ?? await _getCurrentTenantId();
      if (currentTenantId == null) {
        return [];
      }

      final response = await _supabase
          .from('tenant_payments_view')
          .select('*')
          .eq('tenant_id', currentTenantId)
          .order('payment_date', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting recent payments: $e');
      return [];
    }
  }
}
