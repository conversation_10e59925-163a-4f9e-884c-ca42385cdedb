import 'package:flutter/foundation.dart';

/// Model representing an amenity
class Amenity {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final bool isCustom;

  Amenity({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    this.isCustom = false,
  });

  factory Amenity.fromJson(Map<String, dynamic> json, {bool isCustom = false}) {
    return Amenity(
      id: json['id'] ?? '',
      name: json['name'] ?? json['custom_amenity_name'] ?? 'Unknown',
      description: json['description'],
      icon: json['icon'],
      isCustom: isCustom || json['custom_amenity_name'] != null,
    );
  }
}

/// Model representing a property
class Property {
  final String id;
  final String name;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String? description;
  final String? imageUrl;
  final Map<String, dynamic>? additionalInfo;

  Property({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    this.description,
    this.imageUrl,
    this.additionalInfo,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      zipCode: json['zip_code'] ?? '',
      description: json['description'],
      imageUrl: json['image_url'],
      additionalInfo: json['additional_info'],
    );
  }

  String get fullAddress => '$address, $city, $state $zipCode';
}

/// Model representing a room
class Room {
  final String id;
  final String name;
  final String propertyId;
  final String occupancyStatus;
  final String? roomTypeId;
  final String? customRoomType;
  final double rentalPrice;
  final double? size;
  final int? floor;
  final bool isFurnished;
  final String? description;
  final String? imageUrl;
  final String? notes;
  final Map<String, dynamic>? additionalInfo;
  final List<Amenity> amenities;
  final Property? property;

  Room({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.occupancyStatus,
    this.roomTypeId,
    this.customRoomType,
    required this.rentalPrice,
    this.size,
    this.floor,
    required this.isFurnished,
    this.description,
    this.imageUrl,
    this.notes,
    this.additionalInfo,
    this.amenities = const [],
    this.property,
  });

  factory Room.fromJson(Map<String, dynamic> json) {
    // Parse amenities if available
    List<Amenity> amenitiesList = [];
    if (json['room_amenities'] != null) {
      for (var amenityJson in json['room_amenities']) {
        if (amenityJson['amenities'] != null) {
          amenitiesList.add(Amenity.fromJson(amenityJson['amenities']));
        } else if (amenityJson['custom_amenity_name'] != null) {
          amenitiesList.add(Amenity.fromJson(amenityJson, isCustom: true));
        }
      }
    } else if (json['amenities'] != null && json['amenities'] is List) {
      for (var amenityJson in json['amenities']) {
        amenitiesList.add(Amenity.fromJson(amenityJson));
      }
    }

    // Parse property if available
    Property? propertyObj;
    if (json['properties'] != null) {
      propertyObj = Property.fromJson(json['properties']);
    }

    return Room(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      propertyId: json['property_id'] ?? '',
      occupancyStatus: json['occupancy_status'] ?? 'unknown',
      roomTypeId: json['room_type_id'],
      customRoomType: json['custom_room_type'],
      rentalPrice: (json['rental_price'] is num)
          ? json['rental_price'].toDouble()
          : 0.0,
      size: (json['size'] is num) ? json['size'].toDouble() : null,
      floor: json['floor'],
      isFurnished: json['is_furnished'] ?? false,
      description: json['description'],
      imageUrl: json['image_url'],
      notes: json['notes'],
      additionalInfo: json['additional_info'],
      amenities: amenitiesList,
      property: propertyObj,
    );
  }
}

/// Model representing tenant housing details
class TenantHousing {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final DateTime? leaseStartDate;
  final DateTime? leaseEndDate;
  final String status;
  final String? roomId;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final String? notes;
  final String? userId;
  final Room? room;

  TenantHousing({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.leaseStartDate,
    this.leaseEndDate,
    required this.status,
    this.roomId,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.notes,
    this.userId,
    this.room,
  });

  String get fullName => '$firstName $lastName';

  bool get hasValidLease => leaseStartDate != null && leaseEndDate != null;

  int? get daysUntilLeaseEnd {
    if (leaseEndDate == null) return null;
    return leaseEndDate!.difference(DateTime.now()).inDays;
  }

  factory TenantHousing.fromJson(Map<String, dynamic> json) {
    debugPrint('TENANT_HOUSING: fromJson called with: $json');
    
    // Parse dates
    DateTime? leaseStart;
    if (json['lease_start_date'] != null) {
      try {
        leaseStart = DateTime.parse(json['lease_start_date']);
      } catch (e) {
        debugPrint('Error parsing lease_start_date: $e');
      }
    }

    DateTime? leaseEnd;
    if (json['lease_end_date'] != null) {
      try {
        leaseEnd = DateTime.parse(json['lease_end_date']);
      } catch (e) {
        debugPrint('Error parsing lease_end_date: $e');
      }
    }

    // Parse room if available
    Room? roomObj;
    if (json['rooms'] != null) {
      debugPrint('TENANT_HOUSING: Parsing room from: ${json['rooms']}');
      roomObj = Room.fromJson(json['rooms']);
      debugPrint('TENANT_HOUSING: Parsed room object: $roomObj');
      debugPrint('TENANT_HOUSING: Room property: ${roomObj.property}');
    } else {
      debugPrint('TENANT_HOUSING: No rooms data found in JSON');
    }

    final tenantHousing = TenantHousing(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      phoneNumber: json['phone_number'],
      leaseStartDate: leaseStart,
      leaseEndDate: leaseEnd,
      status: json['status'] ?? 'unknown',
      roomId: json['room_id'],
      emergencyContactName: json['emergency_contact_name'],
      emergencyContactPhone: json['emergency_contact_phone'],
      notes: json['notes'],
      userId: json['user_id'],
      room: roomObj,
    );
    
    debugPrint('TENANT_HOUSING: Created TenantHousing object with room: ${tenantHousing.room}');
    return tenantHousing;
  }
}
