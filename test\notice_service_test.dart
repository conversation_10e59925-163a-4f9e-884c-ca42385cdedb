import 'package:flutter_test/flutter_test.dart';
import 'package:residencehub/services/notice_service.dart';
import 'package:residencehub/models/notice.dart';

void main() {
  group('NoticeService', () {
    late NoticeService noticeService;

    setUp(() {
      noticeService = NoticeService();
    });

    test('should create NoticeService instance', () {
      expect(noticeService, isA<NoticeService>());
    });

    test('Notice model should parse JSON correctly', () {
      final json = {
        'id': 'test-id',
        'title': 'Test Notice',
        'content': 'This is a test notice content',
        'type': 'general',
        'priority': 'medium',
        'property_id': 'property-id',
        'author_id': 'author-id',
        'author_name': 'Test Author',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': null,
        'expires_at': null,
        'is_active': true,
        'attachment_urls': null,
        'metadata': null,
        'property_name': 'Test Property',
        'property_address': '123 Test St',
        'property_city': 'Test City',
        'property_state': 'Test State',
      };

      final notice = Notice.fromJson(json);

      expect(notice.id, equals('test-id'));
      expect(notice.title, equals('Test Notice'));
      expect(notice.content, equals('This is a test notice content'));
      expect(notice.type, equals('general'));
      expect(notice.priority, equals('medium'));
      expect(notice.propertyId, equals('property-id'));
      expect(notice.authorId, equals('author-id'));
      expect(notice.authorName, equals('Test Author'));
      expect(notice.isActive, equals(true));
      expect(notice.propertyName, equals('Test Property'));
      expect(notice.propertyAddress, equals('123 Test St'));
      expect(notice.propertyCity, equals('Test City'));
      expect(notice.propertyState, equals('Test State'));
    });

    test('Notice helper methods should work correctly', () {
      final urgentNotice = Notice(
        id: 'urgent-id',
        title: 'Urgent Notice',
        content: 'Urgent content',
        type: 'emergency',
        priority: 'urgent',
        propertyId: 'property-id',
        authorId: 'author-id',
        authorName: 'Author',
        createdAt: DateTime.now(),
        isActive: true,
      );

      final highNotice = Notice(
        id: 'high-id',
        title: 'High Priority Notice',
        content: 'High priority content',
        type: 'maintenance',
        priority: 'high',
        propertyId: 'property-id',
        authorId: 'author-id',
        authorName: 'Author',
        createdAt: DateTime.now(),
        isActive: true,
      );

      final expiredNotice = Notice(
        id: 'expired-id',
        title: 'Expired Notice',
        content: 'Expired content',
        type: 'general',
        priority: 'medium',
        propertyId: 'property-id',
        authorId: 'author-id',
        authorName: 'Author',
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().subtract(const Duration(days: 1)),
        isActive: true,
      );

      expect(urgentNotice.isUrgent, isTrue);
      expect(urgentNotice.isHigh, isFalse);
      expect(urgentNotice.isExpired, isFalse);

      expect(highNotice.isUrgent, isFalse);
      expect(highNotice.isHigh, isTrue);
      expect(highNotice.isExpired, isFalse);

      expect(expiredNotice.isUrgent, isFalse);
      expect(expiredNotice.isHigh, isFalse);
      expect(expiredNotice.isExpired, isTrue);
    });

    test('Notice display names should be correct', () {
      final notice = Notice(
        id: 'test-id',
        title: 'Test Notice',
        content: 'Test content',
        type: 'maintenance',
        priority: 'urgent',
        propertyId: 'property-id',
        authorId: 'author-id',
        authorName: 'Author',
        createdAt: DateTime.now(),
        isActive: true,
      );

      expect(notice.priorityDisplayName, equals('Urgent'));
      expect(notice.typeDisplayName, equals('Maintenance'));
    });

    test('Notice toJson should work correctly', () {
      final notice = Notice(
        id: 'test-id',
        title: 'Test Notice',
        content: 'Test content',
        type: 'general',
        priority: 'medium',
        propertyId: 'property-id',
        authorId: 'author-id',
        authorName: 'Author',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        isActive: true,
        propertyName: 'Test Property',
      );

      final json = notice.toJson();

      expect(json['id'], equals('test-id'));
      expect(json['title'], equals('Test Notice'));
      expect(json['content'], equals('Test content'));
      expect(json['type'], equals('general'));
      expect(json['priority'], equals('medium'));
      expect(json['property_id'], equals('property-id'));
      expect(json['author_id'], equals('author-id'));
      expect(json['author_name'], equals('Author'));
      expect(json['created_at'], equals('2024-01-01T00:00:00.000Z'));
      expect(json['is_active'], equals(true));
      expect(json['property_name'], equals('Test Property'));
    });
  });
}
