import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_settings.dart';

class SettingsService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get user settings
  Future<UserSettings> getUserSettings(String userId) async {
    try {
      final response = await _supabase
          .from('user_settings')
          .select()
          .eq('id', userId)
          .single();

      return UserSettings.fromJson(response);
    } catch (e) {
      // If no settings exist, create default settings
      return await _createDefaultSettings(userId);
    }
  }

  // Create default settings for a user
  Future<UserSettings> _createDefaultSettings(String userId) async {
    final defaultSettings = UserSettings(
      id: userId,
      currencyCode: 'KSH',
      languageCode: 'en',
    );

    await _supabase.from('user_settings').insert(defaultSettings.toJson());

    return defaultSettings;
  }

  // Update currency code
  Future<void> updateCurrencyCode(String userId, String currencyCode) async {
    await _supabase
        .from('user_settings')
        .update({'currency_code': currencyCode})
        .eq('id', userId);
  }

  // Get available currency options
  List<String> getAvailableCurrencies() {
    // For now, we'll just support a few currencies
    // This could be expanded later
    return ['KSH', 'USD', 'EUR', 'GBP'];
  }

  // Get currency symbol for a given currency code
  String getCurrencySymbol(String currencyCode) {
    switch (currencyCode) {
      case 'KSH':
        return 'KSh';
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      default:
        return currencyCode;
    }
  }
}
