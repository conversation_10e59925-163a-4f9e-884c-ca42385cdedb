import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/bill.dart';

/// Utility class for fetching bills for the logged-in tenant
/// Uses the tenant_bills_view which already handles RLS and joins
class BillFetcher {
  static final _supabase = Supabase.instance.client;

  /// Fetch all bills for the currently logged-in tenant
  static Future<List<Bill>> fetchTenantBills({
    BillStatus? status,
    List<BillStatus>? statuses,
    BillType? type,
    bool? includeOverdue,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    String? orderBy = 'due_date',
    bool ascending = true,
  }) async {
    try {
      debugPrint('=== FETCHING TENANT BILLS ===');
      final userId = _supabase.auth.currentUser?.id;
      debugPrint('Current user ID: $userId');
      
      if (userId == null) {
        debugPrint('ERROR: User not authenticated');
        throw Exception('User not authenticated');
      }

      // Get the tenant ID for the current user
      final tenantId = await _getTenantIdByUserId(userId);
      if (tenantId == null) {
        debugPrint('No tenant record found for user: $userId');
        return [];
      }

      // Build the query
      debugPrint('Building tenant_bills_view query for tenant: $tenantId');
      var query = _supabase
          .from('tenant_bills_view')
          .select('*')
          .eq('tenant_id', tenantId);

      // Apply filters
      if (status != null) {
        debugPrint('Filtering by status: ${status.name}');
        query = query.eq('bill_status', status.name);
      }

      if (statuses != null && statuses.isNotEmpty) {
        final statusNames = statuses.map((s) => s.name).toList();
        debugPrint('Filtering by multiple statuses: $statusNames');
        query = query.inFilter('bill_status', statusNames);
      }

      if (type != null) {
        debugPrint('Filtering by type: ${type.name}');
        query = query.eq('bill_type', type.name);
      }

      if (includeOverdue == false) {
        debugPrint('Excluding overdue bills');
        query = query.eq('is_overdue', false);
      } else if (includeOverdue == true) {
        debugPrint('Including only overdue bills');
        query = query.eq('is_overdue', true);
      }

      if (fromDate != null) {
        debugPrint('From date: ${fromDate.toIso8601String()}');
        query = query.gte('due_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        debugPrint('To date: ${toDate.toIso8601String()}');
        query = query.lte('due_date', toDate.toIso8601String());
      }

      // Apply ordering and limit
      debugPrint('Ordering by: $orderBy, ascending: $ascending');
      var transformQuery = query.order(orderBy!, ascending: ascending);

      if (limit != null) {
        debugPrint('Limiting to: $limit records');
        transformQuery = transformQuery.limit(limit);
      }

      debugPrint('Executing query...');
      final response = await transformQuery;
      debugPrint('Query response received: ${response.length} records');
      
      if (response.isEmpty) {
        debugPrint('No bills found for tenant: $tenantId');
        return [];
      }
      
      // Print first record for debugging
      if (response.isNotEmpty) {
        debugPrint('First record sample: ${response[0]}');
      }

      // Convert to Bill objects
      final bills = <Bill>[];
      for (final billData in response) {
        try {
          final billJson = _convertViewToBillJson(billData);
          bills.add(Bill.fromJson(billJson));
        } catch (e) {
          debugPrint('Error parsing bill: $e');
          debugPrint('Problem bill data: $billData');
          // Continue with other bills even if one fails
        }
      }

      debugPrint('Successfully parsed ${bills.length} bills');
      return bills;
    } catch (e) {
      debugPrint('Error fetching tenant bills: $e');
      rethrow;
    }
  }

  /// Fetch bills by specific statuses (pending, overdue, paid, etc.)
  static Future<List<Bill>> fetchBillsByStatus(List<BillStatus> statuses) async {
    return await fetchTenantBills(statuses: statuses);
  }

  /// Fetch only overdue bills
  static Future<List<Bill>> fetchOverdueBills() async {
    return await fetchTenantBills(includeOverdue: true);
  }

  /// Fetch bills due within the next X days
  static Future<List<Bill>> fetchUpcomingBills({int days = 7}) async {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));
    
    return await fetchTenantBills(
      status: BillStatus.pending,
      fromDate: now,
      toDate: futureDate,
      includeOverdue: false,
    );
  }

  /// Fetch bills for a specific month
  static Future<List<Bill>> fetchBillsForMonth(int year, int month) async {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0); // Last day of the month
    
    return await fetchTenantBills(
      fromDate: startDate,
      toDate: endDate,
    );
  }

  /// Fetch recent bills (last 30 days)
  static Future<List<Bill>> fetchRecentBills({int days = 30}) async {
    final startDate = DateTime.now().subtract(Duration(days: days));
    
    return await fetchTenantBills(
      fromDate: startDate,
      limit: 20,
    );
  }

  /// Fetch bills by type (rent, utility, maintenance, etc.)
  static Future<List<Bill>> fetchBillsByType(BillType type) async {
    return await fetchTenantBills(type: type);
  }

  /// Get bill summary for dashboard
  static Future<Map<String, dynamic>> fetchBillsSummary() async {
    try {
      final allBills = await fetchTenantBills();
      
      final summary = <String, dynamic>{
        'total_bills': allBills.length,
        'pending_bills': 0,
        'overdue_bills': 0,
        'paid_bills': 0,
        'cancelled_bills': 0,
        'total_outstanding': 0.0,
        'overdue_amount': 0.0,
        'next_due_bill': null,
        'bills_by_type': <String, int>{},
        'monthly_total': 0.0,
      };

      double totalOutstanding = 0.0;
      double overdueAmount = 0.0;
      double monthlyTotal = 0.0;
      Bill? nextDueBill;
      final billsByType = <String, int>{};
      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      final nextMonth = DateTime(now.year, now.month + 1);

      for (final bill in allBills) {
        // Count by status
        switch (bill.status) {
          case BillStatus.pending:
            summary['pending_bills'] = (summary['pending_bills'] as int) + 1;
            break;
          case BillStatus.overdue:
            summary['overdue_bills'] = (summary['overdue_bills'] as int) + 1;
            break;
          case BillStatus.paid:
            summary['paid_bills'] = (summary['paid_bills'] as int) + 1;
            break;
          case BillStatus.cancelled:
            summary['cancelled_bills'] = (summary['cancelled_bills'] as int) + 1;
            break;
        }

        // Count by type
        final typeName = bill.type.name;
        billsByType[typeName] = (billsByType[typeName] ?? 0) + 1;

        // Calculate outstanding amounts
        if (bill.status != BillStatus.paid && bill.status != BillStatus.cancelled) {
          totalOutstanding += bill.remainingAmount;
          
          if (bill.isOverdue) {
            overdueAmount += bill.remainingAmount;
          }
          
          // Find next due bill
          if (bill.status == BillStatus.pending && !bill.isOverdue) {
            if (nextDueBill == null || bill.dueDate.isBefore(nextDueBill.dueDate)) {
              nextDueBill = bill;
            }
          }
        }

        // Calculate monthly total (bills due this month)
        if (bill.dueDate.isAfter(thisMonth) && bill.dueDate.isBefore(nextMonth)) {
          monthlyTotal += bill.amount;
        }
      }

      summary['total_outstanding'] = totalOutstanding;
      summary['overdue_amount'] = overdueAmount;
      summary['bills_by_type'] = billsByType;
      summary['monthly_total'] = monthlyTotal;

      if (nextDueBill != null) {
        summary['next_due_bill'] = {
          'id': nextDueBill.id,
          'title': nextDueBill.title,
          'amount': nextDueBill.amount,
          'due_date': nextDueBill.dueDate.toIso8601String(),
          'days_until_due': nextDueBill.daysUntilDue,
          'type': nextDueBill.type.name,
        };
      }

      return summary;
    } catch (e) {
      debugPrint('Error getting bills summary: $e');
      return {
        'total_bills': 0,
        'pending_bills': 0,
        'overdue_bills': 0,
        'paid_bills': 0,
        'cancelled_bills': 0,
        'total_outstanding': 0.0,
        'overdue_amount': 0.0,
        'next_due_bill': null,
        'bills_by_type': <String, int>{},
        'monthly_total': 0.0,
      };
    }
  }

  /// Get a specific bill by ID
  static Future<Bill?> fetchBillById(String billId) async {
    try {
      debugPrint('Fetching bill by ID: $billId');
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final tenantId = await _getTenantIdByUserId(userId);
      if (tenantId == null) {
        debugPrint('No tenant record found for user: $userId');
        return null;
      }

      final response = await _supabase
          .from('tenant_bills_view')
          .select('*')
          .eq('tenant_id', tenantId)
          .eq('bill_id', billId)
          .maybeSingle();

      if (response != null) {
        debugPrint('Bill found: ${response['bill_title']}');
        final billJson = _convertViewToBillJson(response);
        return Bill.fromJson(billJson);
      }
      debugPrint('No bill found with ID: $billId');
      return null;
    } catch (e) {
      debugPrint('Error fetching bill by ID: $e');
      return null;
    }
  }

  /// Get bills with advanced filtering options
  static Future<List<Bill>> fetchBillsAdvanced({
    String? searchQuery,
    double? minAmount,
    double? maxAmount,
    List<String>? billNumbers,
    bool? includeInRent,
    List<String>? utilityTypes,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final tenantId = await _getTenantIdByUserId(userId);
      if (tenantId == null) {
        return [];
      }

      var query = _supabase
          .from('tenant_bills_view')
          .select('*')
          .eq('tenant_id', tenantId);

      // Apply advanced filters
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('bill_title.ilike.%$searchQuery%,bill_description.ilike.%$searchQuery%,bill_number.ilike.%$searchQuery%');
      }

      if (minAmount != null) {
        query = query.gte('bill_amount', minAmount);
      }

      if (maxAmount != null) {
        query = query.lte('bill_amount', maxAmount);
      }

      if (billNumbers != null && billNumbers.isNotEmpty) {
        query = query.inFilter('bill_number', billNumbers);
      }

      if (includeInRent != null) {
        query = query.eq('include_in_rent', includeInRent);
      }

      if (utilityTypes != null && utilityTypes.isNotEmpty) {
        query = query.inFilter('utility_type', utilityTypes);
      }

      // Apply ordering - this returns PostgrestTransformBuilder
      final transformQuery = query.order('due_date', ascending: true);

      final response = await transformQuery;

      final bills = <Bill>[];
      for (final billData in response) {
        try {
          final billJson = _convertViewToBillJson(billData);
          bills.add(Bill.fromJson(billJson));
        } catch (e) {
          debugPrint('Error parsing bill: $e');
        }
      }

      return bills;
    } catch (e) {
      debugPrint('Error fetching bills with advanced filters: $e');
      return [];
    }
  }

  /// Private helper method to get tenant ID by user ID
  static Future<String?> _getTenantIdByUserId(String userId) async {
    try {
      debugPrint('Looking up tenant ID for user: $userId');
      
      // First try direct user_id match
      var response = await _supabase
          .from('tenants')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        debugPrint('Found tenant via user_id: ${response['id']}');
        return response['id'];
      }
      
      debugPrint('No tenant found via user_id, trying legacy approach with notes field');

      // Try legacy approach with notes field
      response = await _supabase
          .from('tenants')
          .select('id')
          .like('notes', '%Linked to auth user: $userId%')
          .maybeSingle();

      if (response != null) {
        debugPrint('Found tenant via notes field: ${response['id']}');
        return response['id'];
      }

      debugPrint('No tenant found for user ID: $userId');
      return null;
    } catch (e) {
      debugPrint('Error getting tenant ID: $e');
      return null;
    }
  }

  /// Convert tenant_bills_view data to bill JSON format
  static Map<String, dynamic> _convertViewToBillJson(Map<String, dynamic> viewData) {
    // Add debug logs for key fields to ensure data is available
    debugPrint('Converting view data to bill JSON for bill: ${viewData['bill_title']}');
    
    // Handle null values with safe defaults
    return {
      'id': viewData['bill_id'],
      'title': viewData['bill_title'] ?? 'Unnamed Bill',
      'description': viewData['bill_description'] ?? '',
      'amount': viewData['bill_amount'] ?? 0,
      'due_date': viewData['due_date'] ?? DateTime.now().toIso8601String(),
      'status': viewData['bill_status'] ?? 'pending',
      'type': viewData['bill_type'] ?? 'other',
      'recurrence': viewData['bill_recurrence'] ?? 'oneTime',
      'tenant_id': viewData['tenant_id'],
      'property_id': viewData['property_id'],
      'room_id': viewData['room_id'],
      'created_at': viewData['created_at'] ?? DateTime.now().toIso8601String(),
      'updated_at': viewData['updated_at'],
      'paid_at': viewData['paid_at'],
      'paid_amount': viewData['paid_amount'],
      'notes': viewData['notes'],
      'bill_number': viewData['bill_number'],
      'include_in_rent': viewData['include_in_rent'] ?? false,
      'utility_type': viewData['utility_type'],
      'previous_meter_reading': viewData['previous_meter_reading'],
      'current_meter_reading': viewData['current_meter_reading'],
      'unit_consumed': viewData['unit_consumed'],
      'rate_per_unit': viewData['rate_per_unit'],
      'bill_components': viewData['bill_components'],
    };
  }
}
