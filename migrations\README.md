# Database Migrations

This directory contains SQL migration files for the Tenanta application database.

## Migration Files

This folder contains all database migrations for the Tenanta application.

1. `01_create_properties_table.sql` - Creates properties and utility bills tables
2. `02_create_rooms_and_amenities_tables.sql` - Creates tables for rooms and amenities
3. `03_insert_predefined_amenities.sql` - Inserts default amenities into the database
4. `04_create_views.sql` - Creates database views for common queries
5. `05_create_user_settings_table.sql` - Creates table for user settings
6. `06_create_tenants_and_roles.sql` - Creates tables for tenants and tenant roles
7. `07_add_user_id_to_tenants.sql` - Adds user_id foreign key to tenants table
8. `08_create_activity_logs_table.sql` - Creates activity logs table for tracking user actions
9. `09_create_profiles_table.sql` - Creates user profiles table
10. `10_create_bills_table.sql` - Creates bills table for tenant billing
11. `11_add_gas_utility_bill.sql` - Adds Gas utility bill to all properties that don't already have one
12. `12_create_bill_tenants_table.sql` - Creates bill-tenant relation table
13. `13_create_payments_table.sql` - Creates payments table
14. `14_create_bill_templates_table.sql` - Creates bill templates table
15. `15_create_billing_metadata_table.sql` - Creates billing metadata table
16. `16_update_tenant_bills_view.sql` - Updates tenant bills view
17. `17_create_receipts_table.sql` - Creates receipts table
18. `18_create_expense_tracking_tables.sql` - Creates expense tracking tables

## How to Apply Migrations

Migrations should be applied in numerical order. To apply a migration:

1. Connect to your Supabase project
2. Navigate to the SQL Editor
3. Copy and paste the migration file content
4. Execute the SQL

## Security Configuration

After applying the migrations, configure security settings in the Supabase dashboard:

1. Go to Authentication > Configuration
2. Under "Security" section:
   - Enable "Enable HIBP" to check for leaked passwords
   - Enable "TOTP MFA" for two-factor authentication
   - Consider enabling additional MFA methods like SMS

## Important Notes

- All migrations are designed to be idempotent (can be run multiple times without side effects)
- Each migration starts by dropping any existing objects it will create
- The profile trigger has been updated to handle cases where a profile already exists
- Security settings should be configured in the Supabase dashboard rather than direct SQL
- The migration automatically fixes any missing profiles for existing users

## Schema Overview

The database schema includes:

1. `auth.users` - Managed by Supabase Auth
2. `public.profiles` - User profile information linked to auth.users
3. `public.properties` - Property listings
4. `public.rooms` - Rooms within properties
5. `public.tenants` - Tenant information
6. `public.amenities` - Available amenities for rooms
7. Various junction tables for many-to-many relationships

## Row Level Security (RLS)

All tables have Row Level Security enabled to ensure users can only access their own data or data they have been explicitly granted access to.

## Troubleshooting

If you encounter the error "JSON object requested, multiple (or no) rows returned" when accessing profiles:

1. Make sure the `09_create_profiles_table.sql` migration has been applied
2. Check if the user has a profile in the database
3. The application should automatically create missing profiles, but you can manually run:

```sql
INSERT INTO public.profiles (id, email, full_name)
SELECT id, email, raw_user_meta_data->>'full_name'
FROM auth.users
WHERE id = 'YOUR_USER_ID'
ON CONFLICT (id) DO UPDATE
SET email = EXCLUDED.email, full_name = EXCLUDED.full_name;
```

Replace `YOUR_USER_ID` with the actual user ID.

## How to Run Migrations

You can run these migrations using the Supabase CLI or directly in the Supabase dashboard SQL editor.

### Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the contents of the migration file
5. Run the query

### Using Supabase CLI

1. Install the Supabase CLI if you haven't already:
   ```
   npm install -g supabase
   ```

2. Link your project:
   ```
   supabase link --project-ref YOUR_PROJECT_REF
   ```

3. Run the migration:
   ```
   supabase db push
   ```

## Migration Files

1. `01_create_properties_table.sql` - Creates the properties table and related structures
2. `02_create_rooms_and_amenities_tables.sql` - Creates rooms and amenities tables 
3. `03_insert_predefined_amenities.sql` - Inserts default amenity options
4. `04_create_views_and_tenant_views.sql` - Creates all views for properties, rooms, and tenants
5. `06_create_tenants_and_roles.sql` - Creates tenants table and user role system
7. `10_enhance_auth_security.sql` - Enhances authentication security by enabling leaked password protection and MFA options

## Schema Overview

### Tables

- **properties** - Stores property information (apartments, houses, etc.)
- **utility_bills** - Stores utility bill rates for properties (water, electricity, etc.)
- **rooms** - Stores room information within properties
- **amenities** - Stores predefined and custom amenities
- **room_amenities** - Junction table linking rooms to amenities
- **profiles** - Stores user profile information with secure access controls

### Functions

- `update_profile_updated_at()`: Updates the updated_at timestamp when a profile is modified
- `handle_new_user()`: Creates a profile record when a new auth user is created
- `create_property()`: Creates a new property record
- `update_property()`: Updates an existing property
- `delete_property()`: Soft-deletes a property
- `create_room()`: Creates a new room record
- `update_room()`: Updates an existing room
- `delete_room()`: Soft-deletes a room
- `link_tenant_to_auth_user()`: Links tenant records to auth users

## Security Considerations

- All tables require authentication
- Users can only access their own data
- The profiles table has RLS policies to ensure users can only see and modify their own profiles
- Functions are created with SECURITY DEFINER and explicit search_path to prevent security vulnerabilities
- Leaked password protection and MFA options are enabled for enhanced security

## Running Migrations

To run these migrations, execute them in numerical order on your Supabase database using either:

1. The Supabase dashboard SQL Editor
2. Supabase CLI
3. Direct PostgreSQL client connection

Make sure to run migrations during application deployment or updates.