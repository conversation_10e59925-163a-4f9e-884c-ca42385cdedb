import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import 'screens/login_screen.dart';
import 'screens/signup_screen.dart';
import 'screens/forgot_password_screen.dart';
import 'screens/home_screen.dart';
import 'screens/housing_details_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/bills_screen.dart';
import 'screens/utility_bills_screen.dart';
import 'screens/community_notices_screen.dart';

import 'services/auth_service.dart';
import 'theme/index.dart';
import 'providers/settings_provider.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Load environment variables
    await dotenv.load();
    
    String supabaseUrl = dotenv.env['SUPABASE_URL'] ?? '';
    String supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    
    // Debug environment variables
    if (kDebugMode) {
      debugPrint('=== ENV VARIABLES ===');
      debugPrint('SUPABASE_URL: $supabaseUrl');
      debugPrint('SUPABASE_ANON_KEY length: ${supabaseAnonKey.length}');
      debugPrint('SUPABASE_ANON_KEY first 10 chars: ${supabaseAnonKey.substring(0, min(10, supabaseAnonKey.length))}');
    }
    
    if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
      throw Exception('Missing Supabase configuration in .env file');
    }

    // Initialize Supabase with logging enabled
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: kDebugMode, // Enable debug logging in debug mode
    );
    
    // Enable verbose logging for network requests
    if (kDebugMode) {
      debugPrint('=== SUPABASE INITIALIZED ===');
      debugPrint('URL: $supabaseUrl');
      debugPrint('Debug mode: enabled');
    }
  } catch (e) {
    debugPrint('Error initializing app: $e');
    // Fallback values if .env loading fails
    try {
      await Supabase.initialize(
        url: 'https://qyovaxxljzfpnciumarh.supabase.co',
        anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.xI1BLoCWWTzMiDJM9jaVNuPwdFosznmACQn1BmAFK_8',
        debug: true,
      );
      debugPrint('=== SUPABASE INITIALIZED WITH FALLBACK VALUES ===');
    } catch (fallbackError) {
      debugPrint('Fatal error initializing Supabase: $fallbackError');
      // Show error dialog or screen here in a real app
    }
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeManager()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _initialRoute = '/login';
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _checkInitialRoute();
  }

  Future<void> _checkInitialRoute() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('email');
      final role = prefs.getString('role');

      // Check if user is remembered and has valid session
      if (email != null && role == 'tenant' && _authService.isLoggedIn) {
        setState(() {
          _initialRoute = '/home';
        });

        // Schedule loading settings after the widget is built
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Provider.of<SettingsProvider>(
              context,
              listen: false,
            ).loadSettings();
          }
        });
      }
    } catch (e) {
      // If there's an error, default to login
      setState(() {
        _initialRoute = '/login';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeManager = Provider.of<ThemeManager>(context);

    return MaterialApp(
      title: 'Residence Hub',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeManager.themeMode,
      initialRoute: _initialRoute,
      routes: {
        '/login': (context) => const LoginScreen(),
        '/signup': (context) => const SignupScreen(),
        '/forgot-password': (context) => const ForgotPasswordScreen(),
        '/home': (context) => const HomeScreen(),
        '/housing': (context) => const HousingDetailsScreen(),
        '/profile': (context) => const ProfileScreen(),
        '/settings': (context) => const SettingsScreen(),
        '/bills': (context) => const BillsScreen(),
        '/utility-bills': (context) => const UtilityBillsScreen(),
        '/community-notices': (context) => const CommunityNoticesScreen(),

        // Note: Receipt preview and ticket detail need parameters, so we'll use direct navigation instead of a route
      },
    );
  }
}
