-- Drop existing tables if they exist (in reverse order of dependencies)
DROP TABLE IF EXISTS public.utility_bills CASCADE;
DROP TABLE IF EXISTS public.properties CASCADE;

-- Create properties table
CREATE TABLE public.properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    additional_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create utility_bills table (related to properties)
CREATE TABLE public.utility_bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    rate DECIMAL(10, 2) NOT NULL,
    unit TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_properties_user_id ON public.properties(user_id);
CREATE INDEX idx_utility_bills_property_id ON public.utility_bills(property_id);

-- Enable Row-Level Security
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.utility_bills ENABLE ROW LEVEL SECURITY;

-- Create policies for properties
-- Users can view only their own properties
CREATE POLICY "Users can view their own properties" 
    ON public.properties FOR SELECT
    USING (auth.uid() = user_id);

-- Users can insert their own properties
CREATE POLICY "Users can insert their own properties" 
    ON public.properties FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Users can update their own properties
CREATE POLICY "Users can update their own properties" 
    ON public.properties FOR UPDATE
    USING (auth.uid() = user_id);

-- Users can delete their own properties
CREATE POLICY "Users can delete their own properties" 
    ON public.properties FOR DELETE
    USING (auth.uid() = user_id);

-- Create policies for utility_bills
-- Users can view utility bills for their own properties
CREATE POLICY "Users can view utility bills for their own properties" 
    ON public.utility_bills FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can insert utility bills for their own properties
CREATE POLICY "Users can insert utility bills for their own properties" 
    ON public.utility_bills FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can update utility bills for their own properties
CREATE POLICY "Users can update utility bills for their own properties" 
    ON public.utility_bills FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can delete utility bills for their own properties
CREATE POLICY "Users can delete utility bills for their own properties" 
    ON public.utility_bills FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at timestamp
CREATE TRIGGER update_properties_updated_at
BEFORE UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_utility_bills_updated_at
BEFORE UPDATE ON public.utility_bills
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column(); 