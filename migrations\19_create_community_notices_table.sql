-- Drop all existing objects to ensure clean migration and avoid "already exists" errors

-- Drop functions first (they may depend on the view/table)
DROP FUNCTION IF EXISTS archive_expired_notices() CASCADE;
DROP FUNCTION IF EXISTS get_urgent_notices() CASCADE;
DROP FUNCTION IF EXISTS get_property_notices(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_community_notice_updated_at() CASCADE;

-- Drop triggers
DROP TRIGGER IF EXISTS trigger_update_community_notice_updated_at ON community_notices;

-- Drop view
DROP VIEW IF EXISTS public.active_community_notices CASCADE;
DROP VIEW IF EXISTS active_community_notices CASCADE;

-- Drop policies (must be done before dropping table)
DROP POLICY IF EXISTS "Property managers can manage community notices" ON community_notices;
DROP POLICY IF EXISTS "Tenants can view community notices for their properties" ON community_notices;
DROP POLICY IF EXISTS "Community notices access policy" ON community_notices;

-- Drop indexes
DROP INDEX IF EXISTS idx_community_notices_property_id;
DROP INDEX IF EXISTS idx_community_notices_created_at;
DROP INDEX IF EXISTS idx_community_notices_type;
DROP INDEX IF EXISTS idx_community_notices_priority;
DROP INDEX IF EXISTS idx_community_notices_active;
DROP INDEX IF EXISTS idx_community_notices_expires_at;
DROP INDEX IF EXISTS idx_community_notices_author_id;

-- Drop table
DROP TABLE IF EXISTS community_notices CASCADE;

-- Create community_notices table for property management notifications
CREATE TABLE IF NOT EXISTS community_notices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'general' CHECK (type IN ('general', 'maintenance', 'emergency', 'event', 'policy')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    attachment_urls TEXT[],
    metadata JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_community_notices_property_id ON community_notices(property_id);
CREATE INDEX IF NOT EXISTS idx_community_notices_created_at ON community_notices(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_community_notices_type ON community_notices(type);
CREATE INDEX IF NOT EXISTS idx_community_notices_priority ON community_notices(priority);
CREATE INDEX IF NOT EXISTS idx_community_notices_active ON community_notices(is_active);
CREATE INDEX IF NOT EXISTS idx_community_notices_expires_at ON community_notices(expires_at);
CREATE INDEX IF NOT EXISTS idx_community_notices_author_id ON community_notices(author_id);

-- Enable Row Level Security
ALTER TABLE community_notices ENABLE ROW LEVEL SECURITY;

-- Create a single optimized RLS policy that handles both property managers and tenants
CREATE POLICY "community_notices_unified_policy" ON community_notices AS
    PERMISSIVE FOR ALL
    USING (
        -- Property managers can access notices for their properties
        (property_id IN (
            SELECT id FROM properties WHERE user_id = (SELECT auth.uid())
        ))
        OR 
        -- Tenants can view notices for their properties (only for SELECT operations)
        (
            CASE 
                WHEN (SELECT current_setting('role')) = 'authenticated' AND 
                     (SELECT current_setting('request.method', true)) = 'GET' THEN
                    property_id IN (
                        SELECT p.id
                        FROM properties p
                        JOIN rooms r ON r.property_id = p.id
                        JOIN tenants t ON t.room_id = r.id
                        WHERE t.user_id = (SELECT auth.uid()) AND t.status = 'active'
                    )
                ELSE false
            END
        )
    )
    WITH CHECK (
        -- Only property managers can modify notices
        property_id IN (
            SELECT id FROM properties WHERE user_id = (SELECT auth.uid())
        )
    );

-- Create a view for active notices with property information
-- Note: This view does NOT use SECURITY DEFINER to avoid security issues
-- Instead, it relies on RLS policies on the underlying tables
CREATE VIEW active_community_notices 
WITH (security_invoker = true)
AS
SELECT 
    cn.*,
    p.name as property_name,
    p.address as property_address,
    p.city as property_city,
    p.state as property_state
FROM community_notices cn
JOIN properties p ON cn.property_id = p.id
WHERE cn.is_active = TRUE 
    AND (cn.expires_at IS NULL OR cn.expires_at > NOW())
ORDER BY 
    CASE cn.priority 
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    cn.created_at DESC;

-- Grant permissions on the view
GRANT SELECT ON active_community_notices TO authenticated;

-- Create function to automatically set updated_at
CREATE OR REPLACE FUNCTION update_community_notice_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Create trigger for updated_at
CREATE TRIGGER trigger_update_community_notice_updated_at
    BEFORE UPDATE ON community_notices
    FOR EACH ROW
    EXECUTE FUNCTION update_community_notice_updated_at();

-- Create function to get notices for a specific property
-- Note: Returns TABLE instead of view type to avoid security definer issues
CREATE OR REPLACE FUNCTION get_property_notices(property_uuid UUID)
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    content TEXT,
    type VARCHAR(50),
    priority VARCHAR(20),
    property_id UUID,
    author_id UUID,
    author_name VARCHAR(255),
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN,
    attachment_urls TEXT[],
    metadata JSONB,
    property_name VARCHAR(255),
    property_address TEXT,
    property_city VARCHAR(100),
    property_state VARCHAR(50)
)
LANGUAGE sql
SECURITY INVOKER
SET search_path = public
AS $$
    SELECT
        cn.id, cn.title, cn.content, cn.type, cn.priority, cn.property_id,
        cn.author_id, cn.author_name, cn.created_at, cn.updated_at, cn.expires_at,
        cn.is_active, cn.attachment_urls, cn.metadata,
        p.name as property_name, p.address as property_address,
        p.city as property_city, p.state as property_state
    FROM community_notices cn
    JOIN properties p ON cn.property_id = p.id
    WHERE cn.property_id = property_uuid
        AND cn.is_active = TRUE
        AND (cn.expires_at IS NULL OR cn.expires_at > NOW())
    ORDER BY
        CASE cn.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        cn.created_at DESC;
$$;

-- Create function to get urgent notices for current user
-- Note: Returns TABLE instead of view type and uses SECURITY DEFINER for auth.uid() access
CREATE OR REPLACE FUNCTION get_urgent_notices()
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    content TEXT,
    type VARCHAR(50),
    priority VARCHAR(20),
    property_id UUID,
    author_id UUID,
    author_name VARCHAR(255),
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN,
    attachment_urls TEXT[],
    metadata JSONB,
    property_name VARCHAR(255),
    property_address TEXT,
    property_city VARCHAR(100),
    property_state VARCHAR(50)
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT
        cn.id, cn.title, cn.content, cn.type, cn.priority, cn.property_id,
        cn.author_id, cn.author_name, cn.created_at, cn.updated_at, cn.expires_at,
        cn.is_active, cn.attachment_urls, cn.metadata,
        p.name as property_name, p.address as property_address,
        p.city as property_city, p.state as property_state
    FROM community_notices cn
    JOIN properties p ON cn.property_id = p.id
    WHERE cn.priority IN ('urgent', 'high')
        AND cn.is_active = TRUE
        AND (cn.expires_at IS NULL OR cn.expires_at > NOW())
        AND cn.property_id IN (
            -- Properties owned by user
            SELECT id FROM properties WHERE user_id = (select auth.uid())
            UNION
            -- Properties where user is a tenant
            SELECT p2.id
            FROM properties p2
            JOIN rooms r ON r.property_id = p2.id
            JOIN tenants t ON t.room_id = r.id
            WHERE t.user_id = (select auth.uid()) AND t.status = 'active'
        )
    ORDER BY
        CASE cn.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
        END,
        cn.created_at DESC
    LIMIT 10;
$$;

-- Create function to archive expired notices
CREATE OR REPLACE FUNCTION archive_expired_notices()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    UPDATE community_notices
    SET is_active = FALSE, updated_at = NOW()
    WHERE is_active = TRUE 
    AND expires_at IS NOT NULL 
    AND expires_at <= NOW();
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- Log the archival
    INSERT INTO activity_logs (type, action, details, created_at)
    VALUES (
        'system', 
        'archive_expired_notices', 
        json_build_object(
            'archived_count', archived_count,
            'archived_at', NOW()
        ),
        NOW()
    );
    
    RETURN archived_count;
END;
$$;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_property_notices(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_urgent_notices() TO authenticated;
GRANT EXECUTE ON FUNCTION archive_expired_notices() TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE community_notices IS 'Stores community notices for properties with RLS for property managers and tenants';
COMMENT ON VIEW active_community_notices IS 'View of active, non-expired notices with property information';
COMMENT ON FUNCTION get_property_notices(UUID) IS 'Returns active notices for a specific property';
COMMENT ON FUNCTION get_urgent_notices() IS 'Returns urgent/high priority notices for current user';
COMMENT ON FUNCTION archive_expired_notices() IS 'Archives notices that have passed their expiration date';

-- Insert sample data for testing (optional - remove in production)
-- This will only work if you have existing properties and users
DO $$
BEGIN
    -- Only insert sample data if we have properties
    IF EXISTS (SELECT 1 FROM properties LIMIT 1) THEN
        INSERT INTO community_notices (
            title, content, type, priority, property_id, author_id, author_name, expires_at
        )
        SELECT 
            'Welcome to the Community',
            'Welcome to our property management system. This is a sample notice to demonstrate the community notices feature.',
            'general',
            'medium',
            p.id,
            p.user_id,
            'System Administrator',
            NOW() + INTERVAL '30 days'
        FROM properties p
        LIMIT 1;
        
        RAISE NOTICE 'Sample community notice created successfully';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not create sample data: %', SQLERRM;
END $$;
