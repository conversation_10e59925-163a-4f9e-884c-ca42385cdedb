import 'package:flutter/material.dart';
import '../services/notice_service.dart';
import '../screens/community_notices_screen.dart';

class NoticesSummaryCard extends StatefulWidget {
  const NoticesSummaryCard({super.key});

  @override
  State<NoticesSummaryCard> createState() => _NoticesSummaryCardState();
}

class _NoticesSummaryCardState extends State<NoticesSummaryCard> {
  final NoticeService _noticeService = NoticeService();
  Map<String, dynamic> _summary = {
    'total': 0,
    'urgent': 0,
    'high_priority': 0,
    'unread': 0,
  };
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSummary();
  }

  Future<void> _loadSummary() async {
    try {
      final summary = await _noticeService.getNoticesSummary();
      setState(() {
        _summary = summary;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    final hasUrgentNotices = _summary['urgent'] > 0 || _summary['high_priority'] > 0;

    return Card(
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CommunityNoticesScreen(),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.notifications,
                    color: hasUrgentNotices ? Colors.red : Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Community Notices',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Spacer(),
                  if (_summary['total'] > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: hasUrgentNotices ? Colors.red.shade100 : Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_summary['total']} new',
                        style: TextStyle(
                          color: hasUrgentNotices ? Colors.red.shade700 : Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              if (_summary['total'] > 0) ...[
                if (_summary['urgent'] > 0)
                  _buildNoticeStat(
                    'Urgent notices',
                    _summary['urgent'],
                    Colors.red,
                  ),
                if (_summary['high_priority'] > 0)
                  _buildNoticeStat(
                    'High priority',
                    _summary['high_priority'],
                    Colors.orange,
                  ),
                _buildNoticeStat(
                  'Total notices',
                  _summary['total'],
                  Colors.blue,
                ),
              ] else
                Text(
                  'No new notices',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoticeStat(String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const Spacer(),
          Text(
            count.toString(),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
} 