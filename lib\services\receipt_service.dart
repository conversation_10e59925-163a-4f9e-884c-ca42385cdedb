import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/receipt.dart';
import '../models/bill.dart';

class ReceiptService {
  final _supabase = Supabase.instance.client;

  /// Generate a receipt for a payment
  Future<Receipt?> generateReceipt(String paymentId) async {
    try {
      // Check if receipt already exists for this payment
      final existingReceipt = await _getReceiptByPaymentId(paymentId);
      if (existingReceipt != null) {
        // Receipt already exists, return it
        return existingReceipt;
      }

      // Fetch payment details
      final payment = await _supabase
          .from('tenant_payments_view')
          .select('*')
          .eq('payment_id', paymentId)
          .single();

      // Fetch tenant details with direct deep join
      final tenantId = payment['tenant_id'];
      final tenantDetails = await _supabase
          .from('tenants')
          .select('''
            *,
            rooms!inner(
              *,
              properties!inner(*)
            )
          ''')
          .eq('id', tenantId)
          .single();
      
      // Extract property and room details properly
      final room = tenantDetails['rooms'];
      final property = room != null ? room['properties'] : null;
      
      // Get property and room names with proper fallbacks
      final String propertyName = property != null ? property['name'] : 'Property';
      final String roomName = room != null ? room['name'] : 'Room';

      // Generate a proper receipt reference (use payment reference if available or create a new one)
      final String receiptReference = payment['receipt_reference'] ?? 
                                     'RCP-${DateTime.now().millisecondsSinceEpoch.toString().substring(5)}';

      // Get bill details for all bills in this payment
      final List<dynamic> billIdsList = payment['bill_ids'] ?? [];
      final List<String> billIds = billIdsList
          .map((id) => id.toString())
          .toList();

      // Prepare receipt data
      final receiptData = {
        'payment_id': paymentId,
        'bill_ids': billIds,
        'tenant_id': tenantId,
        'tenant_name': payment['tenant_name'] ?? '${tenantDetails['first_name']} ${tenantDetails['last_name']}',
        'property_name': propertyName,
        'room_name': roomName,
        'amount': payment['amount'] ?? 0.0,
        'payment_date': payment['payment_date'] ?? DateTime.now().toIso8601String(),
        'printed_date': DateTime.now().toIso8601String(),
        'status': payment['status'] ?? 'pending',
        'method': payment['method'],
        'receipt_reference': receiptReference,
        'notes': payment['notes'],
        'created_at': DateTime.now().toIso8601String(),
      };

      // Create receipt object
      final receipt = Receipt.fromJson(receiptData);

      // Save receipt to database
      await _saveReceiptToDatabase(receipt);

      return receipt;
    } catch (e) {
      debugPrint('Error generating receipt: $e');
      return null;
    }
  }

  /// Get receipt by payment ID
  Future<Receipt?> _getReceiptByPaymentId(String paymentId) async {
    try {
      final response = await _supabase
          .from('receipts')
          .select('*')
          .eq('payment_id', paymentId)
          .maybeSingle();

      if (response != null) {
        return Receipt.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting receipt by payment ID: $e');
      return null;
    }
  }

  /// Get a receipt by its ID
  Future<Receipt?> getReceiptById(String receiptId) async {
    try {
      final response = await _supabase
          .from('receipts')
          .select('*')
          .eq('id', receiptId)
          .maybeSingle();

      if (response != null) {
        return Receipt.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting receipt by ID: $e');
      return null;
    }
  }

  /// Get all receipts for a tenant
  Future<List<Receipt>> getTenantReceipts(String tenantId) async {
    try {
      final response = await _supabase
          .from('receipts')
          .select('*')
          .eq('tenant_id', tenantId)
          .order('created_at', ascending: false);

      final receipts = <Receipt>[];
      for (final data in response) {
        receipts.add(Receipt.fromJson(data));
      }
      return receipts;
    } catch (e) {
      debugPrint('Error getting tenant receipts: $e');
      return [];
    }
  }

  /// Get receipts for a specific bill
  Future<List<Receipt>> getReceiptsForBill(String billId) async {
    try {
      // The query checks if the bill_ids array contains the given billId
      final response = await _supabase
          .from('receipts')
          .select('*')
          .filter('bill_ids', 'cs', '{$billId}')
          .order('created_at', ascending: false);

      final receipts = <Receipt>[];
      for (final data in response) {
        receipts.add(Receipt.fromJson(data));
      }
      return receipts;
    } catch (e) {
      debugPrint('Error getting receipts for bill: $e');
      return [];
    }
  }

  /// Save receipt to database
  Future<void> _saveReceiptToDatabase(Receipt receipt) async {
    try {
      // Check if receipt already exists
      final existing = await _supabase
          .from('receipts')
          .select('id')
          .eq('payment_id', receipt.paymentId)
          .maybeSingle();

      if (existing != null) {
        // Update existing receipt
        await _supabase
            .from('receipts')
            .update(receipt.toJson())
            .eq('id', existing['id']);
      } else {
        // Insert new receipt
        await _supabase
            .from('receipts')
            .insert(receipt.toJson());
      }
    } catch (e) {
      debugPrint('Error saving receipt to database: $e');
      rethrow;
    }
  }

  /// Generate PDF receipt
  Future<Uint8List> generatePdfReceipt(Receipt receipt, List<Bill> bills) async {
    final pdf = pw.Document();

    // Load a standard font
    final font = await PdfGoogleFonts.nunitoRegular();
    final fontBold = await PdfGoogleFonts.nunitoBold();
    
    // Format dates
    final dateFormat = DateFormat('MMMM dd, yyyy');

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Container(
            padding: const pw.EdgeInsets.all(20),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                _buildReceiptHeader(receipt, fontBold, font),
                pw.SizedBox(height: 20),
                
                // Receipt Info
                _buildReceiptInfo(receipt, fontBold, font),
                pw.SizedBox(height: 20),
                
                // Bill Details
                _buildBillsTable(bills, fontBold, font),
                pw.SizedBox(height: 20),
                
                // Payment Summary
                _buildPaymentSummary(receipt, bills, fontBold, font),
                pw.SizedBox(height: 20),
                
                // Footer
                _buildReceiptFooter(receipt, fontBold, font, dateFormat),
              ],
            ),
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Build receipt header section
  pw.Widget _buildReceiptHeader(Receipt receipt, pw.Font fontBold, pw.Font font) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'PAYMENT RECEIPT',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 24,
                    color: PdfColors.blue900,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'Reference: ${receipt.receiptReference}',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.grey700,
                  ),
                ),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  receipt.propertyName ?? 'Residence Hub',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 18,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.Text(
                  'Room: ${receipt.roomName ?? 'N/A'}',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 10),
        pw.Divider(color: PdfColors.blue800),
      ],
    );
  }

  /// Build receipt info section
  pw.Widget _buildReceiptInfo(Receipt receipt, pw.Font fontBold, pw.Font font) {
    final dateFormat = DateFormat('MMMM dd, yyyy');
    final timeFormat = DateFormat('hh:mm a');
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'TENANT DETAILS',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 12,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'Name: ${receipt.tenantName ?? 'Unknown'}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  'Property: ${receipt.propertyName ?? 'Unknown'}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  'Room: ${receipt.roomName ?? 'Unknown'}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
              ],
            ),
          ),
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'PAYMENT DETAILS',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 12,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'Date: ${dateFormat.format(receipt.paymentDate)}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  'Time: ${timeFormat.format(receipt.paymentDate)}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  'Method: ${receipt.method?.toUpperCase() ?? 'Unknown'}',
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  'Status: ${receipt.status.name.toUpperCase()}',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 10,
                    color: receipt.status == ReceiptStatus.verified
                        ? PdfColors.green700
                        : receipt.status == ReceiptStatus.rejected
                            ? PdfColors.red700
                            : PdfColors.orange700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build bills table section
  pw.Widget _buildBillsTable(List<Bill> bills, pw.Font fontBold, pw.Font font) {
    final dateFormat = DateFormat('MMM dd, yyyy');
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'BILL DETAILS',
          style: pw.TextStyle(
            font: fontBold,
            fontSize: 14,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: const pw.TableBorder(
            horizontalInside: pw.BorderSide(color: PdfColors.grey300, width: 0.5),
            verticalInside: pw.BorderSide(color: PdfColors.grey300, width: 0.5),
            bottom: pw.BorderSide(color: PdfColors.grey300),
          ),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(1.5),
          },
          children: [
            // Table header
            pw.TableRow(
              decoration: const pw.BoxDecoration(
                color: PdfColors.blue50,
              ),
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    'Description',
                    style: pw.TextStyle(font: fontBold, fontSize: 10),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    'Due Date',
                    style: pw.TextStyle(font: fontBold, fontSize: 10),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    'Type',
                    style: pw.TextStyle(font: fontBold, fontSize: 10),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    'Amount',
                    style: pw.TextStyle(font: fontBold, fontSize: 10),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
              ],
            ),
            // Bills
            ...bills.map((bill) => pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    bill.title,
                    style: pw.TextStyle(font: font, fontSize: 9),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    dateFormat.format(bill.dueDate),
                    style: pw.TextStyle(font: font, fontSize: 9),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    bill.type.name,
                    style: pw.TextStyle(font: font, fontSize: 9),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Text(
                    _formatCurrency(bill.amount),
                    style: pw.TextStyle(font: font, fontSize: 9),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
              ],
            )),
          ],
        ),
      ],
    );
  }

  /// Build payment summary section
  pw.Widget _buildPaymentSummary(Receipt receipt, List<Bill> bills, pw.Font fontBold, pw.Font font) {
    // Calculate totals
    double totalBillAmount = 0;
    for (final bill in bills) {
      totalBillAmount += bill.amount;
    }
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'PAYMENT SUMMARY',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 12,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Total Bill Amount:',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                ),
              ),
              pw.Text(
                _formatCurrency(totalBillAmount),
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Amount Paid:',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 12,
                ),
              ),
              pw.Text(
                _formatCurrency(receipt.amount),
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 12,
                  color: PdfColors.green800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build receipt footer section
  pw.Widget _buildReceiptFooter(Receipt receipt, pw.Font fontBold, pw.Font font, DateFormat dateFormat) {
    final timeFormat = DateFormat('hh:mm a');
    
    return pw.Column(
      children: [
        pw.Divider(color: PdfColors.grey300),
        pw.SizedBox(height: 10),
        pw.Center(
          child: pw.Text(
            'Thank you for your payment!',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 12,
              color: PdfColors.blue800,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.SizedBox(height: 5),
        pw.Center(
          child: pw.Text(
            'This receipt was generated on ${dateFormat.format(receipt.printedDate)} at ${timeFormat.format(receipt.printedDate)}',
            style: pw.TextStyle(
              font: font,
              fontSize: 8,
              color: PdfColors.grey600,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Format currency
  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
    return formatter.format(amount);
  }
} 