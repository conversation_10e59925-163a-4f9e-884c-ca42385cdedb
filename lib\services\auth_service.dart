import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get current user
  User? get currentUser => _supabase.auth.currentUser;

  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Format error messages for better user experience
  String formatErrorMessage(dynamic error) {
    final errorStr = error.toString();
    
    // Handle authentication errors
    if (errorStr.contains('AuthException') || 
        errorStr.contains('AuthApiError')) {
      
      // Common authentication errors
      if (errorStr.contains('Invalid login credentials')) {
        return 'Invalid email or password. Please try again.';
      } else if (errorStr.contains('Email not confirmed')) {
        return 'Please verify your email before logging in.';
      } else if (errorStr.contains('User already registered')) {
        return 'An account with this email already exists.';
      } else if (errorStr.contains('Password should be at least')) {
        return 'Password must be at least 6 characters long.';
      } else if (errorStr.contains('Invalid OTP')) {
        return 'Invalid verification code. Please try again.';
      } else if (errorStr.contains('Rate limit exceeded')) {
        return 'Too many attempts. Please try again later.';
      }
    }
    
    // Network errors
    if (errorStr.contains('SocketException') || 
        errorStr.contains('NetworkException')) {
      return 'Network error. Please check your connection and try again.';
    }
    
    // For custom exceptions thrown in our code
    if (errorStr.startsWith('Exception: ')) {
      return errorStr.substring(11); // Remove "Exception: " prefix
    }
    
    // Default error message
    return 'An error occurred. Please try again.';
  }

  // Show success message
  void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  // Build consistent error widget
  Widget buildErrorWidget(String errorMessage) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      margin: const EdgeInsets.only(bottom: 16, top: 8),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade700,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              errorMessage,
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    return await _supabase.auth.signUp(
      email: email,
      password: password,
      emailRedirectTo: null,
    );
  }

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await _supabase.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  // Sign out
  Future<void> signOut() async {
    await _supabase.auth.signOut();
    // Clear remembered credentials
    await clearRememberedCredentials();
  }

  // Save user credentials and load settings
  Future<void> saveUserCredentialsAndLoadSettings(
    BuildContext context, {
    required String email,
    required String role,
    bool rememberMe = false,
  }) async {
    if (rememberMe) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('email', email);
      await prefs.setString('role', role);
    }

    // Load user settings
    if (context.mounted) {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      await settingsProvider.loadSettings();
    }
  }

  // Clear remembered credentials
  Future<void> clearRememberedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('email');
    await prefs.remove('role');
  }

  // Check if user has tenant role
  bool hasTenantRole() {
    final user = currentUser;
    if (user != null) {
      final role = user.userMetadata?['role'];
      return role == 'tenant';
    }
    return false;
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      debugPrint('Attempting password reset for: $email');
      await _supabase.auth.resetPasswordForEmail(email, redirectTo: null);
      debugPrint('Password reset email sent successfully');
    } catch (e) {
      debugPrint('Password reset error: $e');
      rethrow; // Rethrow to handle in the UI
    }
  }

  // Verify OTP for password reset
  Future<AuthResponse> verifyOTP({
    required String email,
    required String token,
    required String type,
  }) async {
    try {
      debugPrint('Verifying OTP for: $email, type: $type');
      final response = await _supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: type == 'signup' ? OtpType.signup : OtpType.recovery,
      );
      debugPrint('OTP verification successful');
      return response;
    } catch (e) {
      debugPrint('OTP verification error: $e');
      rethrow; // Rethrow to handle in the UI
    }
  }

  // Update user password
  Future<UserResponse> updatePassword({required String password}) async {
    try {
      debugPrint('Updating password');
      final response = await _supabase.auth.updateUser(UserAttributes(password: password));
      debugPrint('Password updated successfully');
      return response;
    } catch (e) {
      debugPrint('Password update error: $e');
      rethrow; // Rethrow to handle in the UI
    }
  }
}
