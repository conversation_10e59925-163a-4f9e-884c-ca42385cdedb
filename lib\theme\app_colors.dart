import 'package:flutter/material.dart';

/// App color palette for Residence Hub
class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF2A6FDB);
  static const Color primaryDark = Color(0xFF1A5BC7);
  static const Color primaryLight = Color(0xFF5E92F3);

  // Secondary colors
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryDark = Color(0xFF018786);
  static const Color secondaryLight = Color(0xFF60EBE4);

  // Accent colors
  static const Color accent = Color(0xFFFF9800);
  static const Color accentDark = Color(0xFFE68A00);
  static const Color accentLight = Color(0xFFFFB74D);

  // Background colors
  static const Color background = Color(0xFFF5F7FA);
  static const Color surface = Colors.white;
  static const Color card = Colors.white;

  // Text colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);

  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFFC107);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Other colors
  static const Color divider = Color(0xFFEEEEEE);
  static const Color disabled = Color(0xFFBDBDBD);
  static const Color shadow = Color(0x1A000000);
}

/// Extension methods for Color class
extension ColorExtensions on Color {
  /// Returns a new color with the specified values
  Color withValues({int? red, int? green, int? blue, int? alpha}) {
    return Color.fromARGB(
      alpha ?? ((a * 255.0).round() & 0xFF),
      red ?? ((r * 255.0).round() & 0xFF),
      green ?? ((g * 255.0).round() & 0xFF),
      blue ?? ((b * 255.0).round() & 0xFF),
    );
  }
}
