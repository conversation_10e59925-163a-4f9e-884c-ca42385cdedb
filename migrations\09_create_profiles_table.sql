-- =============================================
-- COMPREHENSIVE DATABASE MIGRATION
-- =============================================
-- This migration:
-- 1. Creates the profiles table with proper RLS policies
-- 2. Sets up triggers for user profile management
-- 3. Enhances auth security settings
-- 4. Fixes any missing profiles for existing users
-- =============================================

-- Drop existing objects if they exist to make migration idempotent
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profile_updated_at ON public.profiles;
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.update_profile_updated_at();
DROP POLICY IF EXISTS "Users can read only their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update only their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert only their own profile" ON public.profiles;

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT,
  full_name TEXT,
  phone_number TEXT,
  address TEXT,
  date_of_birth DATE,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to read only their own profile
CREATE POLICY "Users can read only their own profile" ON public.profiles
  FOR SELECT USING (id = (SELECT auth.uid()));

-- Create policy to allow users to update only their own profile
CREATE POLICY "Users can update only their own profile" ON public.profiles
  FOR UPDATE USING (id = (SELECT auth.uid()));

-- Create policy to allow users to insert only their own profile
CREATE POLICY "Users can insert only their own profile" ON public.profiles
  FOR INSERT WITH CHECK (id = (SELECT auth.uid()));

-- Create function to automatically update the updated_at field
CREATE OR REPLACE FUNCTION update_profile_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create trigger to automatically update the updated_at field
CREATE TRIGGER update_profile_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW EXECUTE FUNCTION update_profile_updated_at();

-- Create function to handle new user creation with improved error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if profile already exists to avoid duplicate key errors
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    -- Profile already exists, update it instead
    UPDATE public.profiles
    SET 
      email = NEW.email,
      full_name = NEW.raw_user_meta_data->>'full_name',
      updated_at = NOW()
    WHERE id = NEW.id;
  ELSE
    -- Profile doesn't exist, insert new one
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
      NEW.id, 
      NEW.email,
      NEW.raw_user_meta_data->>'full_name'
    );
  END IF;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the transaction
    RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create trigger to automatically create a profile when a new user is created
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =============================================
-- ENHANCE AUTH SECURITY SETTINGS
-- =============================================

-- Note: These settings should be configured in the Supabase dashboard
-- instead of direct SQL as the auth schema may vary between Supabase versions
DO $$
BEGIN
  -- Add a notice about configuring security settings in the dashboard
  RAISE NOTICE 'Security settings should be configured in the Supabase dashboard:';
  RAISE NOTICE '1. Go to Authentication > Configuration';
  RAISE NOTICE '2. Enable "Enable HIBP" to check for leaked passwords';
  RAISE NOTICE '3. Enable "TOTP MFA" for two-factor authentication';
  RAISE NOTICE '4. Consider enabling additional MFA methods like SMS';
END $$;

-- =============================================
-- FIX MISSING PROFILES FOR EXISTING USERS
-- =============================================

-- Create missing profiles for any existing users
INSERT INTO public.profiles (
  id, 
  email, 
  full_name,
  created_at,
  updated_at
)
SELECT 
  au.id, 
  au.email, 
  au.raw_user_meta_data->>'full_name',
  NOW(),
  NOW()
FROM 
  auth.users au
LEFT JOIN 
  public.profiles p ON au.id = p.id
WHERE 
  p.id IS NULL
ON CONFLICT (id) 
DO UPDATE SET 
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- Log completion message
DO $$
BEGIN
  RAISE NOTICE 'Migration completed successfully';
END $$; 