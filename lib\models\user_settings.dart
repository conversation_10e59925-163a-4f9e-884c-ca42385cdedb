class UserSettings {
  final String id;
  final String currencyCode;
  final String languageCode;
  final DateTime? updatedAt;
  final DateTime? createdAt;

  UserSettings({
    required this.id,
    required this.currencyCode,
    required this.languageCode,
    this.updatedAt,
    this.createdAt,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      id: json['id'] as String,
      currencyCode: json['currency_code'] as String? ?? 'KSH',
      languageCode: json['language_code'] as String? ?? 'en',
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currency_code': currencyCode,
      'language_code': languageCode,
      'updated_at': updatedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  UserSettings copyWith({
    String? currencyCode,
    String? languageCode,
  }) {
    return UserSettings(
      id: id,
      currencyCode: currencyCode ?? this.currencyCode,
      languageCode: languageCode ?? this.languageCode,
      updatedAt: updatedAt,
      createdAt: createdAt,
    );
  }
} 