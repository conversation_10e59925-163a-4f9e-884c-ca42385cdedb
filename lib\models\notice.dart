class Notice {
  final String id;
  final String title;
  final String content;
  final String type;
  final String priority;
  final String propertyId;
  final String authorId;
  final String authorName;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? expiresAt;
  final bool isActive;
  final List<String>? attachmentUrls;
  final Map<String, dynamic>? metadata;
  final String? propertyName;
  final String? propertyAddress;
  final String? propertyCity;
  final String? propertyState;

  Notice({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.propertyId,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
    this.updatedAt,
    this.expiresAt,
    required this.isActive,
    this.attachmentUrls,
    this.metadata,
    this.propertyName,
    this.propertyAddress,
    this.propertyCity,
    this.propertyState,
  });

  factory Notice.fromJson(Map<String, dynamic> json) {
    return Notice(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: json['type'] as String,
      priority: json['priority'] as String,
      propertyId: json['property_id'] as String,
      authorId: json['author_id'] as String,
      authorName: json['author_name'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
      isActive: json['is_active'] as bool,
      attachmentUrls: json['attachment_urls'] != null 
          ? List<String>.from(json['attachment_urls'] as List) 
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      propertyName: json['property_name'] as String?,
      propertyAddress: json['property_address'] as String?,
      propertyCity: json['property_city'] as String?,
      propertyState: json['property_state'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'priority': priority,
      'property_id': propertyId,
      'author_id': authorId,
      'author_name': authorName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'is_active': isActive,
      'attachment_urls': attachmentUrls,
      'metadata': metadata,
      'property_name': propertyName,
      'property_address': propertyAddress,
      'property_city': propertyCity,
      'property_state': propertyState,
    };
  }

  // Helper methods for UI
  bool get isUrgent => priority == 'urgent';
  bool get isHigh => priority == 'high';
  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  
  String get priorityDisplayName {
    switch (priority) {
      case 'urgent':
        return 'Urgent';
      case 'high':
        return 'High';
      case 'medium':
        return 'Medium';
      case 'low':
        return 'Low';
      default:
        return priority;
    }
  }

  String get typeDisplayName {
    switch (type) {
      case 'general':
        return 'General';
      case 'maintenance':
        return 'Maintenance';
      case 'emergency':
        return 'Emergency';
      case 'event':
        return 'Event';
      case 'policy':
        return 'Policy';
      default:
        return type;
    }
  }

  @override
  String toString() {
    return 'Notice{id: $id, title: $title, type: $type, priority: $priority}';
  }
}
