
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import '../models/bill.dart';

import '../utils/currency_formatter.dart';
import '../services/bill_service.dart';
import '../screens/receipt_preview_screen.dart';


class BillDetailScreen extends StatefulWidget {
  final Bill bill;

  const BillDetailScreen({
    super.key,
    required this.bill,
  });
  
  @override
  State<BillDetailScreen> createState() => _BillDetailScreenState();
}

class _BillDetailScreenState extends State<BillDetailScreen> {
  Bill get bill => widget.bill;

  @override
  Widget build(BuildContext context) {
    final isOverdue = bill.isOverdue;
    final isPaid = bill.status == BillStatus.paid;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Bill Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareBill(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header card with bill status
            _buildHeaderCard(context, isOverdue, isPaid),
            
            const SizedBox(height: 16),
            
            // Bill information card
            _buildBillInfoCard(context),
            
            const SizedBox(height: 16),
            
            // Utility details (if applicable)
            if (bill.type == BillType.utility && 
                (bill.utilityType != null || 
                 bill.previousMeterReading != null ||
                 bill.currentMeterReading != null)) ...[
              _buildUtilityDetailsCard(context),
              const SizedBox(height: 16),
            ],
            
            // Payment information (if paid)
            if (isPaid) ...[
              _buildPaymentInfoCard(context),
              const SizedBox(height: 16),
            ],
            
            // Additional notes (if any)
            if (bill.notes != null && bill.notes!.isNotEmpty) ...[
              _buildNotesCard(context),
              const SizedBox(height: 16),
            ],
            
            // Action buttons
            if (!isPaid) _buildActionButtons(context, isOverdue),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, bool isOverdue, bool isPaid) {
    Color statusColor;
    IconData statusIcon;
    String statusText;
    
    if (isPaid) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'PAID';
    } else if (isOverdue) {
      statusColor = Colors.red;
      statusIcon = Icons.warning;
      statusText = 'OVERDUE';
    } else {
      statusColor = Colors.orange;
      statusIcon = Icons.pending;
      statusText = 'PENDING';
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              statusColor.withAlpha(204),
              statusColor.withAlpha(230),
            ],
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    statusIcon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        bill.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Amount
            Text(
              CurrencyFormatter.format(context, bill.amount),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            if (bill.billNumber != null) ...[
              const SizedBox(height: 8),
              Text(
                'Bill #${bill.billNumber}',
                style: TextStyle(
                  color: Colors.white.withAlpha(204),
                  fontSize: 16,
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Due date row
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(38),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Due Date',
                          style: TextStyle(
                            color: Colors.white.withAlpha(204),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          DateFormat('EEEE, MMMM d, y').format(bill.dueDate),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isOverdue && !isPaid)
                          Text(
                            'Overdue by ${(-bill.daysUntilDue).abs()} day${(-bill.daysUntilDue).abs() == 1 ? '' : 's'}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        else if (!isPaid && bill.daysUntilDue >= 0)
                          Text(
                            '${bill.daysUntilDue} day${bill.daysUntilDue == 1 ? '' : 's'} remaining',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBillInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bill Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow(
              context,
              'Type',
              bill.type.name.toUpperCase(),
              Icons.category,
            ),
            
            _buildInfoRow(
              context,
              'Description',
              bill.description,
              Icons.description,
            ),
            
            _buildInfoRow(
              context,
              'Recurrence',
              bill.recurrence.name.toUpperCase(),
              Icons.repeat,
            ),
            
            _buildInfoRow(
              context,
              'Created',
              DateFormat('MMM d, y • h:mm a').format(bill.createdAt),
              Icons.access_time,
            ),
            
            if (bill.updatedAt != null)
              _buildInfoRow(
                context,
                'Last Updated',
                DateFormat('MMM d, y • h:mm a').format(bill.updatedAt!),
                Icons.update,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUtilityDetailsCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Utility Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (bill.utilityType != null)
              _buildInfoRow(
                context,
                'Utility Type',
                bill.utilityType!.name.toUpperCase(),
                Icons.electrical_services,
              ),
            
            if (bill.previousMeterReading != null)
              _buildInfoRow(
                context,
                'Previous Reading',
                bill.previousMeterReading!.toStringAsFixed(2),
                Icons.history,
              ),
            
            if (bill.currentMeterReading != null)
              _buildInfoRow(
                context,
                'Current Reading',
                bill.currentMeterReading!.toStringAsFixed(2),
                Icons.speed,
              ),
            
            if (bill.unitConsumed != null)
              _buildInfoRow(
                context,
                'Units Consumed',
                bill.unitConsumed!.toStringAsFixed(2),
                Icons.straighten,
              ),
            
            if (bill.ratePerUnit != null)
              _buildInfoRow(
                context,
                'Rate per Unit',
                CurrencyFormatter.format(context, bill.ratePerUnit!),
                Icons.attach_money,
              ),
            
            if (bill.includeInRent)
              _buildInfoRow(
                context,
                'Billing',
                'INCLUDED IN RENT',
                Icons.home,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.green.shade50,
          border: Border.all(color: Colors.green.shade200),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Payment Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade800,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (bill.paidAt != null)
              _buildInfoRow(
                context,
                'Paid On',
                DateFormat('EEEE, MMMM d, y • h:mm a').format(bill.paidAt!),
                Icons.payment,
                valueColor: Colors.green.shade700,
              ),
            
            if (bill.paidAmount != null)
              _buildInfoRow(
                context,
                'Amount Paid',
                CurrencyFormatter.format(context, bill.paidAmount!),
                Icons.attach_money,
                valueColor: Colors.green.shade700,
              ),
            
            // Add View Receipt button for paid bills
            if (bill.status == BillStatus.paid)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () => _viewReceipt(context),
                    icon: const Icon(Icons.receipt_long),
                    label: const Text('View Receipt'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.green.shade700,
                      side: BorderSide(color: Colors.green.shade300),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Notes',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                bill.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.outline,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: valueColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isOverdue) {
    return Column(
      children: [
        if (isOverdue) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _handlePayNow(context),
              icon: const Icon(Icons.payment),
              label: const Text('Pay Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        
        if (bill.status == BillStatus.paid) ...[
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _viewReceipt(context),
              icon: const Icon(Icons.receipt_long),
              label: const Text('View Receipt'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _handlePayNow(BuildContext context) {
    // Show payment options dialog or navigate to payment screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Payment functionality coming soon!'),
      ),
    );
  }

  void _viewReceipt(BuildContext context) async {
    // Store scaffoldMessenger and navigator before async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);
    
    try {
      // Get payment for this bill
      final billService = BillService();
      final payments = await billService.getPaymentsForBill(bill.id);
      
      if (!mounted) return;
      
      if (payments.isEmpty) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('No payment receipt found for this bill.'),
          ),
        );
        return;
      }
      
      // Use the most recent payment (first in the list)
      final paymentId = payments.first['payment_id'];
      
      // Navigate to receipt preview screen
      if (!mounted) return;
      
      navigator.push(
        MaterialPageRoute(
          builder: (context) => ReceiptPreviewScreen(paymentId: paymentId),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error viewing receipt: $e'),
        ),
      );
    }
  }



  void _shareBill(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Share Bill',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      tooltip: 'Close',
                    ),
                  ],
                ),
              ),
              ListTile(
                leading: const Icon(Icons.text_fields),
                title: const Text('Share as Text'),
                onTap: () => _shareAsText(),
              ),
              ListTile(
                leading: const Icon(Icons.picture_as_pdf),
                title: const Text('Share as PDF'),
                onTap: () => _shareAsPdf(context),
              ),
            ],
          ),
        );
      },
    );
  }

  void _shareAsText() {
    final dateFormat = DateFormat('MMM d, y');
    final timeFormat = DateFormat('h:mm a');

    // Build comprehensive bill information
    final StringBuffer billText = StringBuffer();

    // Header
    billText.writeln('📄 BILL DETAILS');
    billText.writeln('=' * 30);
    billText.writeln();

    // Basic Information
    billText.writeln('📋 Basic Information:');
    billText.writeln('• Title: ${bill.title}');
    if (bill.billNumber != null) {
      billText.writeln('• Bill Number: ${bill.billNumber}');
    }
    billText.writeln('• Description: ${bill.description}');
    billText.writeln('• Type: ${_getBillTypeDisplayName(bill.type)}');
    billText.writeln('• Status: ${_getBillStatusDisplayName(bill.status)}');
    billText.writeln();

    // Financial Information
    billText.writeln('💰 Financial Details:');
    billText.writeln('• Amount: \$${bill.amount.toStringAsFixed(2)}');
    if (bill.paidAmount != null && bill.paidAmount! > 0) {
      billText.writeln('• Paid Amount: \$${bill.paidAmount!.toStringAsFixed(2)}');
      billText.writeln('• Remaining: \$${bill.remainingAmount.toStringAsFixed(2)}');
    }
    billText.writeln();

    // Dates
    billText.writeln('📅 Important Dates:');
    billText.writeln('• Due Date: ${dateFormat.format(bill.dueDate)}');
    billText.writeln('• Created: ${dateFormat.format(bill.createdAt)} at ${timeFormat.format(bill.createdAt)}');
    if (bill.paidAt != null) {
      billText.writeln('• Paid On: ${dateFormat.format(bill.paidAt!)} at ${timeFormat.format(bill.paidAt!)}');
    }
    billText.writeln();

    // Utility-specific information
    if (bill.type == BillType.utility && bill.utilityType != null) {
      billText.writeln('⚡ Utility Information:');
      billText.writeln('• Utility Type: ${_getUtilityTypeDisplayName(bill.utilityType!)}');
      if (bill.previousMeterReading != null && bill.currentMeterReading != null) {
        billText.writeln('• Previous Reading: ${bill.previousMeterReading}');
        billText.writeln('• Current Reading: ${bill.currentMeterReading}');
        billText.writeln('• Units Consumed: ${bill.unitConsumed ?? 0}');
      }
      if (bill.ratePerUnit != null) {
        billText.writeln('• Rate per Unit: \$${bill.ratePerUnit!.toStringAsFixed(2)}');
      }
      billText.writeln('• Include in Rent: ${bill.includeInRent ? 'Yes' : 'No'}');
      billText.writeln();
    }

    // Recurrence
    billText.writeln('🔄 Recurrence: ${_getRecurrenceDisplayName(bill.recurrence)}');
    billText.writeln();

    // Additional Notes
    if (bill.notes != null && bill.notes!.isNotEmpty) {
      billText.writeln('📝 Notes:');
      billText.writeln(bill.notes);
      billText.writeln();
    }

    // Status indicators
    if (bill.isOverdue) {
      billText.writeln('⚠️ This bill is OVERDUE');
    } else if (bill.status == BillStatus.paid) {
      billText.writeln('✅ This bill has been PAID');
    } else {
      final daysUntilDue = bill.daysUntilDue;
      if (daysUntilDue > 0) {
        billText.writeln('⏰ Due in $daysUntilDue day${daysUntilDue == 1 ? '' : 's'}');
      } else if (daysUntilDue == 0) {
        billText.writeln('⏰ Due TODAY');
      }
    }

    billText.writeln();
    billText.writeln('Generated from ResidenceHub');

    // Share the text
    SharePlus.instance.share(
      ShareParams(
        text: billText.toString(),
        subject: 'Bill Details - ${bill.title}',
      ),
    );
  }

  void _shareAsPdf(BuildContext context) {
    // Show a snackbar indicating PDF generation is in progress
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('PDF sharing feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );

    // TODO: Implement PDF generation and sharing
    // This would involve creating a PDF document similar to the receipt service
    // and then sharing it using the same pattern as in receipt_preview_screen.dart
  }

  String _getBillTypeDisplayName(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'Rent';
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.other:
        return 'Other';
    }
  }

  String _getBillStatusDisplayName(BillStatus status) {
    switch (status) {
      case BillStatus.paid:
        return 'Paid';
      case BillStatus.pending:
        return 'Pending';
      case BillStatus.overdue:
        return 'Overdue';
      case BillStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _getUtilityTypeDisplayName(UtilityType type) {
    switch (type) {
      case UtilityType.water:
        return 'Water';
      case UtilityType.electricity:
        return 'Electricity';
      case UtilityType.gas:
        return 'Gas';
      case UtilityType.internet:
        return 'Internet';
      case UtilityType.other:
        return 'Other';
    }
  }

  String _getRecurrenceDisplayName(RecurrenceType recurrence) {
    switch (recurrence) {
      case RecurrenceType.oneTime:
        return 'One Time';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.quarterly:
        return 'Quarterly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }
}
