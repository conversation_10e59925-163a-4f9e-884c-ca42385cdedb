import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:printing/printing.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import '../services/bill_service.dart';
import '../services/receipt_service.dart';
import '../models/bill.dart';
import '../models/receipt.dart';

class ReceiptPreviewScreen extends StatefulWidget {
  final String paymentId;
  
  const ReceiptPreviewScreen({
    super.key,
    required this.paymentId,
  });

  @override
  State<ReceiptPreviewScreen> createState() => _ReceiptPreviewScreenState();
}

class _ReceiptPreviewScreenState extends State<ReceiptPreviewScreen> {
  final BillService _billService = BillService();
  final ReceiptService _receiptService = ReceiptService();
  
  bool _isLoading = true;
  Uint8List? _pdfDocument;
  String? _errorMessage;
  Receipt? _receipt;

  @override
  void initState() {
    super.initState();
    _loadReceiptData();
  }

  Future<void> _loadReceiptData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Generate or fetch receipt using receipt service
      final receipt = await _receiptService.generateReceipt(widget.paymentId);
      if (receipt == null) {
        throw Exception('Could not generate receipt');
      }

      // Store receipt for filename generation
      _receipt = receipt;

      // Get bills for this payment
      final List<Bill> bills = [];
      for (final billId in receipt.billIds) {
        final bill = await _billService.getBillById(billId);
        if (bill != null) {
          bills.add(bill);
        }
      }

      // Generate PDF
      final pdfDocument = await _receiptService.generatePdfReceipt(receipt, bills);

      // Update state
      if (mounted) {
        setState(() {
          _pdfDocument = pdfDocument;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt Preview'),
        actions: [
          if (!_isLoading && _pdfDocument != null)
          IconButton(
            icon: const Icon(Icons.share),
              onPressed: () => _sharePdf(_pdfDocument!),
              tooltip: 'Share receipt',
            ),
          if (!_isLoading && _pdfDocument != null)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: () => _savePdf(_pdfDocument!),
              tooltip: 'Save receipt',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
                      const Icon(Icons.error_outline, 
              color: Colors.red,
                        size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading receipt',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32.0),
                        child: Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
                        ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadReceiptData,
                        child: const Text('Retry'),
            ),
          ],
        ),
                )
              : _pdfDocument == null
                  ? const Center(child: Text('No receipt data found'))
                  : PdfPreview(
            build: (format) => _pdfDocument!,
                      allowSharing: true,
            allowPrinting: true,
                      pdfFileName: _generateFileName(),
                    ),
    );
  }

  // Generate a meaningful filename for the receipt
  String _generateFileName() {
    if (_receipt == null) return 'receipt.pdf';
    
    // Format: Receipt_Reference_TenantName_Date.pdf
    final sanitizedReference = _receipt!.receiptReference.replaceAll(RegExp(r'[^\w]'), '_');
    final sanitizedTenantName = _receipt!.tenantName?.replaceAll(RegExp(r'[^\w]'), '_') ?? 'Tenant';
    final date = _receipt!.paymentDate.toString().split(' ')[0];
    
    return 'Receipt_${sanitizedReference}_${sanitizedTenantName}_$date.pdf';
  }

  Future<void> _sharePdf(Uint8List pdfBytes) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = _generateFileName();
      final file = File('${tempDir.path}/$fileName');
      
      await file.writeAsBytes(pdfBytes);
      
      final result = await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path, mimeType: 'application/pdf')],
          text: 'Here is your receipt',
          subject: 'Payment Receipt',
          sharePositionOrigin: Rect.fromLTWH(0, 0, 1, 1),
        ),
      );
      
      debugPrint('Share result: ${result.status}');
    } catch (e) {
      _showErrorSnackbar('Failed to share receipt: ${e.toString()}');
    }
  }

  Future<void> _savePdf(Uint8List pdfBytes) async {
    try {
      if (Platform.isAndroid) {
        // Use Downloads directory directly without permission request
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          _showErrorSnackbar('Could not access storage directory');
          return;
        }
        
        // Get path to Downloads folder
        final downloadsPath = "${directory.path.split('/Android')[0]}/Download";
        
        // Generate unique filename
        final fileName = _generateFileName();
        final file = File('$downloadsPath/$fileName');
        
        await file.writeAsBytes(pdfBytes);
        
        _showSuccessSnackbar('Receipt saved to Downloads folder');
      } else if (Platform.isIOS) {
        // For iOS - use documents directory
        final directory = await getApplicationDocumentsDirectory();
        final fileName = _generateFileName();
        final file = File('${directory.path}/$fileName');
        await file.writeAsBytes(pdfBytes);
        
        _showSuccessSnackbar('Receipt saved to Documents folder');
      } else {
        // Desktop platforms - show save dialog
        String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Receipt',
          fileName: _generateFileName(),
          type: FileType.custom,
          allowedExtensions: ['pdf'],
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsBytes(pdfBytes);
          _showSuccessSnackbar('Receipt saved successfully');
        }
      }
    } catch (e) {
      _showErrorSnackbar('Failed to save receipt: ${e.toString()}');
    }
  }
  
  void _showSuccessSnackbar(String message) {
        ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
