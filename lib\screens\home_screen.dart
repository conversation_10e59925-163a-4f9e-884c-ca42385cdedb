import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/tenant_service.dart';
import '../services/bill_service.dart';
import '../models/tenant_housing.dart';
import '../widgets/theme_toggle.dart';
import '../widgets/welcome_card.dart';
import '../widgets/bills_summary_card.dart';
import '../widgets/notices_summary_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../theme/index.dart';
import 'dart:async';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _timer;
  late DateTime _currentTime;
  final AuthService authService = AuthService();
  final TenantService tenantService = TenantService();
  final BillService billService = BillService();

  // Cache for tenant details to prevent constant reloading
  Future<Map<String, dynamic>?>? _tenantDetailsFuture;
  Future<TenantHousing?>? _housingDetailsFuture;
  Future<Map<String, dynamic>>? _billsSummaryFuture;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    // Update time every minute instead of every second to reduce rebuilds
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });

    // Initialize cached futures
    _loadData();
  }

  void _loadData() {
    final userId = authService.currentUser?.id;
    _tenantDetailsFuture = tenantService.getTenantDetails(userId);
    _housingDetailsFuture = tenantService.getTenantHousingDetails(userId);
    _billsSummaryFuture = billService.getBillsSummary();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Residence Hub'),
        centerTitle: true,
        actions: [
          const ThemeSelector(),
        ],
      ),
      drawer: FutureBuilder<Map<String, dynamic>?>(
        future: _tenantDetailsFuture,
        builder: (context, tenantSnapshot) {
          // Get user name and email from tenant data
          final String userName = tenantSnapshot.data?['full_name'] ?? 'Resident';
          final String userEmail = tenantSnapshot.data?['email'] ?? '<EMAIL>';

          return FutureBuilder<TenantHousing?>(
            future: _housingDetailsFuture,
            builder: (context, housingSnapshot) {
              // Get property and room details
              final String propertyName = housingSnapshot.data?.room?.property?.name ?? 'Property';
              final String roomName = housingSnapshot.data?.room?.name ?? 'Room';

              return Drawer(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withValues(alpha: 0.8),
                        Colors.white,
                      ],
                      stops: const [0.0, 0.3, 0.3],
                    ),
                  ),
                  child: Column(
                    children: <Widget>[
                      // Modern Header Section
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.fromLTRB(24, 60, 24, 32),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Profile Avatar with Status Indicator
                            Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      width: 3,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.2),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: CircleAvatar(
                                    backgroundColor: Colors.white,
                                    radius: 35,
                                    child: Text(
                                      userName.isNotEmpty ? userName[0].toUpperCase() : 'R',
                                      style: TextStyle(
                                        fontSize: 28,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 2,
                                  right: 2,
                                  child: Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: Colors.green,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // User Info
                            Text(
                              userName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              userEmail,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.8),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 12),

                            // Property Info Card
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 16,
                                    color: Colors.white.withValues(alpha: 0.9),
                                  ),
                                  const SizedBox(width: 6),
                                  Flexible(
                                    child: Text(
                                      "$propertyName • $roomName",
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.white.withValues(alpha: 0.9),
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Menu Items Section
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: ListView(
                            padding: EdgeInsets.zero,
                            children: <Widget>[
                              const SizedBox(height: 8),
                              _buildDrawerItem(
                                context,
                                icon: Icons.home_rounded,
                                title: 'Home',
                                subtitle: 'Dashboard overview',
                                isSelected: true,
                                onTap: () => Navigator.pop(context),
                              ),
                              _buildDrawerItem(
                                context,
                                icon: Icons.meeting_room_rounded,
                                title: 'Room Details',
                                subtitle: 'View your accommodation',
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.pushNamed(context, '/housing');
                                },
                              ),
                              _buildDrawerItem(
                                context,
                                icon: Icons.receipt_long_rounded,
                                title: 'Bills',
                                subtitle: 'Manage your payments',
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.pushNamed(context, '/bills');
                                },
                              ),
                              _buildDrawerItem(
                                context,
                                icon: Icons.bolt_rounded,
                                title: 'Utility Bills',
                                subtitle: 'Track utility usage',
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.pushNamed(context, '/utility-bills');
                                },
                              ),
                              _buildDrawerItem(
                                context,
                                icon: Icons.settings_rounded,
                                title: 'Settings',
                                subtitle: 'App preferences',
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.pushNamed(context, '/settings');
                                },
                              ),

                              // Divider with spacing
                              const Padding(
                                padding: EdgeInsets.symmetric(vertical: 16),
                                child: Divider(
                                  thickness: 1,
                                  indent: 16,
                                  endIndent: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
<<<<<<< HEAD
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: <Widget>[
                          ListTile(
                            leading: const Icon(Icons.home),
                            title: const Text('Home'),
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.meeting_room),
                            title: const Text('Room Details'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/housing');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.receipt_long),
                            title: const Text('Bills'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/bills');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.bolt),
                            title: const Text('Utility Bills'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/utility-bills');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.notifications),
                            title: const Text('Community Notices'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/community-notices');
                            },
                          ),
=======
                      // Bottom Actions
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        child: Column(
                          children: [
                            _buildDrawerItem(
                              context,
                              icon: Icons.person_rounded,
                              title: 'Profile',
                              subtitle: 'Manage your account',
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.pushNamed(context, '/profile');
                              },
                            ),
                            const SizedBox(height: 8),
                            _buildLogoutItem(context),
                            const SizedBox(height: 16),
>>>>>>> 230118d2f97c40ca8fb0e3f1a6c1da05af77363d

                            // App Version Footer
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.home_rounded,
                                    size: 16,
                                    color: Colors.grey[500],
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'Residence Hub v1.0.0',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[500],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _loadData();
          });
        },
        child: Padding(
          padding: AppThemeConstants.screenPadding,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Card with Date, Time, and Property Info
                FutureBuilder<TenantHousing?>(
                  future: _housingDetailsFuture,
                  builder: (context, housingSnapshot) {
                    return FutureBuilder<Map<String, dynamic>?>(
                      future: _tenantDetailsFuture,
                      builder: (context, tenantSnapshot) {
                        final String userName =
                            tenantSnapshot.data?['first_name'] ??
                            tenantSnapshot.data?['full_name']
                                ?.toString()
                                .split(' ')
                                .first ??
                            'Resident';

                        // Debug logging
                        debugPrint('HOME_SCREEN: Housing snapshot data: ${housingSnapshot.data}');
                        debugPrint('HOME_SCREEN: Housing snapshot hasData: ${housingSnapshot.hasData}');
                        debugPrint('HOME_SCREEN: Housing snapshot connectionState: ${housingSnapshot.connectionState}');
                        debugPrint('HOME_SCREEN: Housing snapshot error: ${housingSnapshot.error}');
                        debugPrint('HOME_SCREEN: Tenant snapshot data: ${tenantSnapshot.data}');
                        debugPrint('HOME_SCREEN: User name: $userName');

                        return WelcomeCard(
                          userName: userName,
                          currentTime: _currentTime,
                          housingDetails: housingSnapshot.data,
                        );
                      },
                    );
                  },
                ),

                const SizedBox(height: 20),
                
                // Bills Summary Section
                FutureBuilder<Map<String, dynamic>>(
                  future: _billsSummaryFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppThemeConstants.radiusL),
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      );
                    }

                    return BillsSummaryCard(
                      summary: snapshot.data ?? {},
                    );
                  },
                ),

                const SizedBox(height: 20),

                // Community Notices Summary
                const NoticesSummaryCard(),

                const SizedBox(height: 20),

                // Quick Actions Grid
                const QuickActionsGrid(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isSelected
            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
            : Colors.transparent,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          splashColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          highlightColor: Theme.of(context).primaryColor.withValues(alpha: 0.05),
          child: ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: isSelected
                ? Colors.white
                : Colors.grey[600],
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 15,
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey[800],
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        trailing: isSelected
            ? Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: Theme.of(context).primaryColor,
              )
            : null,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutItem(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.2),
        ),
        color: Colors.red.withValues(alpha: 0.05),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
          Navigator.pop(context);
          // Show confirmation dialog
          final shouldLogout = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Logout'),
              content: const Text('Are you sure you want to logout?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  child: const Text('Logout'),
                ),
              ],
            ),
          );

          if (shouldLogout == true && context.mounted) {
            await authService.signOut();
            if (context.mounted) {
              Navigator.pushReplacementNamed(context, '/login');
            }
          }
          },
          borderRadius: BorderRadius.circular(12),
          splashColor: Colors.red.withValues(alpha: 0.1),
          highlightColor: Colors.red.withValues(alpha: 0.05),
          child: ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.logout_rounded,
            size: 20,
            color: Colors.red,
          ),
        ),
        title: const Text(
          'Logout',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: Colors.red,
          ),
        ),
        subtitle: Text(
          'Sign out of your account',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          ),
        ),
      ),
    );
  }
}
