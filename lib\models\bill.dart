import 'package:uuid/uuid.dart';

enum BillStatus { paid, pending, overdue, cancelled }

enum BillType { rent, utility, maintenance, service, other }

enum RecurrenceType { oneTime, monthly, quarterly, yearly }

enum UtilityType { water, electricity, gas, internet, other }

class Bill {
  final String id;
  final String title;
  final String description;
  final double amount;
  final DateTime dueDate;
  final BillStatus status;
  final BillType type;
  final RecurrenceType recurrence;
  final String? tenantId;
  final String? propertyId;
  final String? roomId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? paidAt;
  final double? paidAmount;
  final String? notes;
  final String? billNumber;
  
  // Utility-specific fields
  final bool includeInRent;
  final UtilityType? utilityType;
  final double? previousMeterReading;
  final double? currentMeterReading;
  final double? unitConsumed;
  final double? ratePerUnit;
  final List<Map<String, dynamic>>? billComponents;

  Bill({
    String? id,
    required this.title,
    required this.description,
    required this.amount,
    required this.dueDate,
    this.status = BillStatus.pending,
    required this.type,
    required this.recurrence,
    this.tenantId,
    this.propertyId,
    this.roomId,
    DateTime? createdAt,
    this.updatedAt,
    this.paidAt,
    this.paidAmount,
    this.notes,
    this.billNumber,
    this.includeInRent = false,
    this.utilityType,
    this.previousMeterReading,
    this.currentMeterReading,
    this.unitConsumed,
    this.ratePerUnit,
    this.billComponents,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  // Factory constructor from JSON
  factory Bill.fromJson(Map<String, dynamic> json) {
    return Bill(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      amount: (json['amount'] ?? 0).toDouble(),
      dueDate: DateTime.parse(json['due_date']),
      status: _parseStatus(json['status']),
      type: _parseType(json['type']),
      recurrence: _parseRecurrence(json['recurrence']),
      tenantId: json['tenant_id'],
      propertyId: json['property_id'],
      roomId: json['room_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      paidAt: json['paid_at'] != null 
          ? DateTime.parse(json['paid_at']) 
          : null,
      paidAmount: json['paid_amount']?.toDouble(),
      notes: json['notes'],
      billNumber: json['bill_number'],
      includeInRent: json['include_in_rent'] ?? false,
      utilityType: json['utility_type'] != null 
          ? _parseUtilityType(json['utility_type']) 
          : null,
      previousMeterReading: json['previous_meter_reading']?.toDouble(),
      currentMeterReading: json['current_meter_reading']?.toDouble(),
      unitConsumed: json['unit_consumed']?.toDouble(),
      ratePerUnit: json['rate_per_unit']?.toDouble(),
      billComponents: json['bill_components'] != null
          ? List<Map<String, dynamic>>.from(json['bill_components'])
          : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'due_date': dueDate.toIso8601String(),
      'status': status.name,
      'type': type.name,
      'recurrence': recurrence.name,
      'tenant_id': tenantId,
      'property_id': propertyId,
      'room_id': roomId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'paid_at': paidAt?.toIso8601String(),
      'paid_amount': paidAmount,
      'notes': notes,
      'bill_number': billNumber,
      'include_in_rent': includeInRent,
      'utility_type': utilityType?.name,
      'previous_meter_reading': previousMeterReading,
      'current_meter_reading': currentMeterReading,
      'unit_consumed': unitConsumed,
      'rate_per_unit': ratePerUnit,
      'bill_components': billComponents,
    };
  }

  // Parse status from string
  static BillStatus _parseStatus(String? status) {
    if (status == null) return BillStatus.pending;
    switch (status.toLowerCase()) {
      case 'paid':
        return BillStatus.paid;
      case 'overdue':
        return BillStatus.overdue;
      case 'cancelled':
        return BillStatus.cancelled;
      case 'pending':
      default:
        return BillStatus.pending;
    }
  }

  // Parse type from string
  static BillType _parseType(String? type) {
    if (type == null) return BillType.other;
    switch (type.toLowerCase()) {
      case 'rent':
        return BillType.rent;
      case 'utility':
        return BillType.utility;
      case 'maintenance':
        return BillType.maintenance;
      case 'service':
        return BillType.service;
      case 'other':
      default:
        return BillType.other;
    }
  }

  // Parse recurrence from string
  static RecurrenceType _parseRecurrence(String? recurrence) {
    if (recurrence == null) return RecurrenceType.oneTime;
    switch (recurrence.toLowerCase()) {
      case 'monthly':
        return RecurrenceType.monthly;
      case 'quarterly':
        return RecurrenceType.quarterly;
      case 'yearly':
        return RecurrenceType.yearly;
      case 'onetime':
      default:
        return RecurrenceType.oneTime;
    }
  }

  // Parse utility type from string
  static UtilityType _parseUtilityType(String? type) {
    if (type == null) return UtilityType.other;
    switch (type.toLowerCase()) {
      case 'water':
        return UtilityType.water;
      case 'electricity':
        return UtilityType.electricity;
      case 'gas':
        return UtilityType.gas;
      case 'internet':
        return UtilityType.internet;
      case 'other':
      default:
        return UtilityType.other;
    }
  }

  // Helper methods
  bool get isOverdue => status != BillStatus.paid && 
                      status != BillStatus.cancelled && 
                      dueDate.isBefore(DateTime.now());

  double get remainingAmount => amount - (paidAmount ?? 0);

  bool get isFullyPaid => status == BillStatus.paid || 
                         (paidAmount != null && paidAmount! >= amount);

  bool get isPartiallyPaid => paidAmount != null && 
                             paidAmount! > 0 && 
                             paidAmount! < amount;

  // Get display color based on status
  String get statusColor {
    switch (status) {
      case BillStatus.paid:
        return 'green';
      case BillStatus.overdue:
        return 'red';
      case BillStatus.cancelled:
        return 'grey';
      case BillStatus.pending:
        return isOverdue ? 'red' : 'orange';
    }
  }

  // Get days until due (negative if overdue)
  int get daysUntilDue {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final due = DateTime(dueDate.year, dueDate.month, dueDate.day);
    return due.difference(today).inDays;
  }

  // Copy with method for updating bills
  Bill copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    DateTime? dueDate,
    BillStatus? status,
    BillType? type,
    RecurrenceType? recurrence,
    String? tenantId,
    String? propertyId,
    String? roomId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? paidAt,
    double? paidAmount,
    String? notes,
    String? billNumber,
    bool? includeInRent,
    UtilityType? utilityType,
    double? previousMeterReading,
    double? currentMeterReading,
    double? unitConsumed,
    double? ratePerUnit,
    List<Map<String, dynamic>>? billComponents,
  }) {
    return Bill(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      type: type ?? this.type,
      recurrence: recurrence ?? this.recurrence,
      tenantId: tenantId ?? this.tenantId,
      propertyId: propertyId ?? this.propertyId,
      roomId: roomId ?? this.roomId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      paidAt: paidAt ?? this.paidAt,
      paidAmount: paidAmount ?? this.paidAmount,
      notes: notes ?? this.notes,
      billNumber: billNumber ?? this.billNumber,
      includeInRent: includeInRent ?? this.includeInRent,
      utilityType: utilityType ?? this.utilityType,
      previousMeterReading: previousMeterReading ?? this.previousMeterReading,
      currentMeterReading: currentMeterReading ?? this.currentMeterReading,
      unitConsumed: unitConsumed ?? this.unitConsumed,
      ratePerUnit: ratePerUnit ?? this.ratePerUnit,
      billComponents: billComponents ?? this.billComponents,
    );
  }
}
